import { describe, it, expect, beforeAll, afterAll, beforeEach } from "@jest/globals";
import { PCCParser } from "../../src/sites/pcc";
import { TenderData } from "../../../shared/types";
import { Logger } from "../../src/core/utils";
import { FileManager } from "../../src/core/FileManager";
import { readFileSync, writeFileSync, existsSync, unlinkSync } from "fs";
import { join } from "path";

describe("Parser Daemon Comprehensive Test", () => {
  let parser: PCCParser;
  const timeout = 60000; // 60-second timeout
  const TEST_LIMIT = 10; // 限制測試只處理 10 個項目
  const testOutputDir = join(__dirname, "../../test-output");

  beforeAll(() => {
    Logger.setVerbose(true);
    parser = new PCCParser();
    console.log("🚀 Starting comprehensive parser daemon test");
    
    // 確保測試輸出目錄存在
    FileManager.ensureDir(join(testOutputDir, "dummy.txt"));
  });

  afterAll(() => {
    console.log("✅ Comprehensive parser daemon test completed");
  });

  beforeEach(() => {
    // 清理測試輸出檔案
    const testFiles = [
      join(testOutputDir, "114-06-26.json"),
      join(testOutputDir, "test-batch.json"),
      join(testOutputDir, "incremental-test.json")
    ];
    
    testFiles.forEach(file => {
      if (existsSync(file)) {
        unlinkSync(file);
      }
    });
  });

  describe("1. 指定區段內標案爬取與解析", () => {
    it("should parse tender list from HTML file and limit to 10 items", async () => {
      const listFile = join(__dirname, "../../list/114-06-26.html.gz");
      
      // 檢查檔案是否存在
      expect(existsSync(listFile)).toBe(true);
      
      // 讀取並解析列表頁面
      const html = await FileManager.readFile(listFile);
      expect(html.length).toBeGreaterThan(1000);
      
      // 解析標案列表
      const items = await parser.parseList(html, "114-06-26");
      console.log(`📋 Found ${items.length} tender items in list`);
      
      expect(items.length).toBeGreaterThan(0);
      
      // 限制只處理前 10 個項目
      const limitedItems = items.slice(0, TEST_LIMIT);
      console.log(`🔬 Limited to ${limitedItems.length} items for testing`);
      
      expect(limitedItems.length).toBeLessThanOrEqual(TEST_LIMIT);
      
      // 驗證每個項目都有必要的欄位
      limitedItems.forEach((item, index) => {
        expect(item.id).toBeDefined();
        expect(item.title).toBeDefined();
        expect(item.url).toBeDefined();
        console.log(`  [${index + 1}] ${item.id}: ${item.title?.substring(0, 50)}...`);
      });
    }, timeout);
  });

  describe("2. 去重複化機制測試", () => {
    it("should remove duplicate tenders based on ID", async () => {
      const listFile = join(__dirname, "../../list/114-06-26.html.gz");
      const html = await FileManager.readFile(listFile);
      const items = await parser.parseList(html, "114-06-26");
      
      // 創建一些重複的項目來測試去重
      const duplicatedItems = [
        ...items.slice(0, 5),
        ...items.slice(0, 3), // 重複前 3 個
        ...items.slice(5, 8)
      ];
      
      console.log(`📊 Original items: ${items.slice(0, 8).length}, With duplicates: ${duplicatedItems.length}`);
      
      // 實現去重邏輯
      const uniqueItems = duplicatedItems.filter((item, index, array) => 
        array.findIndex(i => i.id === item.id) === index
      );
      
      console.log(`✨ After deduplication: ${uniqueItems.length}`);
      
      expect(uniqueItems.length).toBe(8); // 應該是 5 + 3 = 8 個唯一項目
      expect(uniqueItems.length).toBeLessThan(duplicatedItems.length);
      
      // 驗證沒有重複的 ID
      const ids = uniqueItems.map(item => item.id);
      const uniqueIds = [...new Set(ids)];
      expect(ids.length).toBe(uniqueIds.length);
    });
  });

  describe("3. 錯誤處理和 Robust 機制", () => {
    it("should handle errors gracefully and continue processing", async () => {
      const listFile = join(__dirname, "../../list/114-06-26.html.gz");
      const html = await FileManager.readFile(listFile);
      const items = await parser.parseList(html, "114-06-26");
      const limitedItems = items.slice(0, TEST_LIMIT);
      
      const results: TenderData[] = [];
      const errors: string[] = [];
      let successCount = 0;
      let failureCount = 0;
      
      console.log(`🔄 Processing ${limitedItems.length} items with error handling...`);
      
      for (let i = 0; i < limitedItems.length; i++) {
        const item = limitedItems[i];
        
        try {
          console.log(`[${i + 1}/${limitedItems.length}] Processing: ${item.title?.substring(0, 40)}...`);
          
          // 模擬一些失敗情況來測試錯誤處理
          if (i === 2 || i === 5) {
            throw new Error(`Simulated network error for item ${i + 1}`);
          }
          
          // 嘗試爬取詳細資料（這裡可能會真的失敗）
          let tenderData: TenderData;
          
          try {
            tenderData = await parser.fetchTenderDetail(item.url);
            // 補充基本資訊
            if (item.id) tenderData.tender_id = item.id;
            if (item.title) tenderData.title = item.title;
            if (item.agency) tenderData.agency_name = item.agency;
          } catch (networkError) {
            // 如果網路爬取失敗，創建一個 fallback 資料
            console.log(`⚠️ Network crawl failed, using fallback data: ${networkError}`);
            tenderData = {
              tender_id: item.id || "unknown",
              case_number: item.id || "unknown",
              project_id: item.id || "unknown",
              pcc_main_key: item.id || "unknown",
              title: item.title || "Unknown Title",
              description: "Fallback data due to crawl failure",
              category: "其他",
              category_code: "99",
              category_type: "other" as const,
              agency_code: "unknown",
              agency_name: item.agency || "Unknown Agency",
              agency_unit: "Unknown Unit",
              agency_address: "Unknown Address",
              contact_person: "",
              contact_phone: "",
              contact_fax: "",
              contact_email: "",
              budget_amount: 0,
              budget_disclosed: false,
              budget_range: "未公開",
              announcement_date: new Date(),
              status: "bidding" as const,
              tender_type: "tender" as const,
              procurement_method: "公開招標",
              award_method: "最低標",
              performance_location: "未指定",
              performance_location_detail: "未指定",
              performance_period: "未指定",
              is_wto_gpa: false,
              is_anztec: false,
              is_astep: false,
              is_multiple_award: false,
              is_joint_procurement: false,
              is_electronic_bidding: false,
              requires_deposit: false,
              is_turnkey: false,
              requires_engineer_cert: false,
              is_special_procurement: false,
              ai_score: 5,
              source_url: item.url,
              publish_date: new Date(),
              created_at: new Date(),
              updated_at: new Date(),
              data_version: "1.0",
              is_government_subsidy: false,
              is_disaster_reconstruction: false,
              is_sensitive_security: false,
              is_published_gazette: false,
              history: []
            };
          }
          
          results.push(tenderData);
          successCount++;
          console.log(`✅ [${i + 1}] Success: ${tenderData.title?.substring(0, 30)}...`);
          
        } catch (error) {
          failureCount++;
          const errorMsg = `Failed to process item ${i + 1} (${item.id}): ${error}`;
          errors.push(errorMsg);
          console.log(`❌ [${i + 1}] ${errorMsg}`);
          
          // 重要：即使失敗也要繼續處理下一個項目
          continue;
        }
        
        // 添加延遲避免被封
        if (i < limitedItems.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      console.log(`📊 Processing Summary:`);
      console.log(`   Total items: ${limitedItems.length}`);
      console.log(`   Successful: ${successCount}`);
      console.log(`   Failed: ${failureCount}`);
      console.log(`   Success rate: ${((successCount / limitedItems.length) * 100).toFixed(1)}%`);
      
      // 驗證錯誤處理機制
      expect(results.length + errors.length).toBe(limitedItems.length);
      expect(errors.length).toBeGreaterThan(0); // 應該有一些模擬的錯誤
      expect(results.length).toBeGreaterThan(0); // 應該有一些成功的結果
      
      // 保存結果供後續測試使用
      const outputData = {
        sourceFile: "114-06-26.html.gz",
        processedAt: new Date().toISOString(),
        itemCount: limitedItems.length,
        tenderCount: results.length,
        successCount,
        failureCount,
        successRate: `${((successCount / limitedItems.length) * 100).toFixed(1)}%`,
        items: limitedItems,
        tenders: results,
        errors
      };
      
      await FileManager.writeJsonFile(
        join(testOutputDir, "test-batch.json"),
        outputData
      );
      
      console.log(`💾 Test results saved to test-batch.json`);
    }, 120000); // 增加超時時間到 2 分鐘
  });

  describe("4. 中斷和恢復功能（增量模式）", () => {
    it("should skip already processed files in incremental mode", async () => {
      const testOutputFile = join(testOutputDir, "incremental-test.json");
      
      // 第一次處理：創建一個已處理的檔案
      const mockProcessedData = {
        sourceFile: "114-06-26.html.gz",
        processedAt: new Date().toISOString(),
        itemCount: 5,
        tenderCount: 5,
        items: [],
        tenders: []
      };
      
      await FileManager.writeJsonFile(testOutputFile, mockProcessedData);
      console.log("📝 Created mock processed file for incremental test");
      
      // 驗證檔案存在
      expect(existsSync(testOutputFile)).toBe(true);
      
      // 模擬增量模式邏輯
      const inputFile = join(__dirname, "../../list/114-06-26.html.gz");
      const outputFile = testOutputFile;
      
      // 檢查是否已處理（增量模式邏輯）
      const alreadyProcessed = existsSync(outputFile);
      console.log(`🔍 File already processed: ${alreadyProcessed}`);
      
      if (alreadyProcessed) {
        const existingData = JSON.parse(readFileSync(outputFile, "utf-8"));
        console.log(`📋 Found existing processed data:`);
        console.log(`   Processed at: ${existingData.processedAt}`);
        console.log(`   Item count: ${existingData.itemCount}`);
        console.log(`   Tender count: ${existingData.tenderCount}`);
        
        expect(existingData.sourceFile).toBe("114-06-26.html.gz");
        expect(existingData.itemCount).toBe(5);
      }
      
      // 在增量模式下，應該跳過已處理的檔案
      expect(alreadyProcessed).toBe(true);
      console.log("✅ Incremental mode correctly detected already processed file");
    });
  });

  describe("5. 完整工作流程整合測試", () => {
    it("should execute complete workflow with all features", async () => {
      console.log("🎯 Starting complete workflow integration test...");
      
      const listFile = join(__dirname, "../../list/114-06-26.html.gz");
      const outputFile = join(testOutputDir, "114-06-26.json");
      
      // 步驟 1: 解析列表
      const html = await FileManager.readFile(listFile);
      const allItems = await parser.parseList(html, "114-06-26");
      console.log(`📋 Step 1: Parsed ${allItems.length} items from list`);
      
      // 步驟 2: 去重複化
      const uniqueItems = allItems.filter((item, index, array) => 
        array.findIndex(i => i.id === item.id) === index
      );
      console.log(`✨ Step 2: After deduplication: ${uniqueItems.length} unique items`);
      
      // 步驟 3: 限制數量
      const limitedItems = uniqueItems.slice(0, TEST_LIMIT);
      console.log(`🔬 Step 3: Limited to ${limitedItems.length} items for testing`);
      
      // 步驟 4: 批次處理與錯誤處理
      const results: TenderData[] = [];
      const errors: string[] = [];
      const BATCH_SIZE = 3;
      
      for (let i = 0; i < limitedItems.length; i += BATCH_SIZE) {
        const batch = limitedItems.slice(i, i + BATCH_SIZE);
        const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
        const totalBatches = Math.ceil(limitedItems.length / BATCH_SIZE);
        
        console.log(`🔄 Step 4: Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`);
        
        for (let j = 0; j < batch.length; j++) {
          const item = batch[j];
          const itemIndex = i + j + 1;
          
          try {
            // 模擬一些失敗來測試錯誤處理
            if (itemIndex === 3 || itemIndex === 7) {
              throw new Error(`Simulated error for item ${itemIndex}`);
            }
            
            // 創建 fallback 資料（避免真實網路請求）
            const tenderData: TenderData = {
              tender_id: item.id || "test",
              case_number: item.id || "test",
              project_id: item.id || "test",
              pcc_main_key: item.id || "test",
              title: item.title || "Test Title",
              description: `Test description for ${item.title}`,
              category: "測試類別",
              category_code: "99",
              category_type: "other" as const,
              agency_code: "test",
              agency_name: item.agency || "Test Agency",
              agency_unit: "Test Unit",
              agency_address: "Test Address",
              contact_person: "Test Contact",
              contact_phone: "02-1234-5678",
              contact_fax: "02-1234-5679",
              contact_email: "<EMAIL>",
              budget_amount: Math.floor(Math.random() * 1000000),
              budget_disclosed: true,
              budget_range: "100萬以上未達1000萬",
              announcement_date: new Date(),
              status: "bidding" as const,
              tender_type: "tender" as const,
              procurement_method: "公開招標",
              award_method: "最低標",
              performance_location: "測試地點",
              performance_location_detail: "測試地點詳細",
              performance_period: "30日曆天",
              is_wto_gpa: false,
              is_anztec: false,
              is_astep: false,
              is_multiple_award: false,
              is_joint_procurement: false,
              is_electronic_bidding: true,
              requires_deposit: true,
              deposit_amount: 50000,
              is_turnkey: false,
              requires_engineer_cert: false,
              is_special_procurement: false,
              ai_score: Math.floor(Math.random() * 10) + 1,
              source_url: item.url,
              publish_date: new Date(),
              created_at: new Date(),
              updated_at: new Date(),
              data_version: "1.0",
              is_government_subsidy: false,
              is_disaster_reconstruction: false,
              is_sensitive_security: false,
              is_published_gazette: true,
              history: []
            };
            
            results.push(tenderData);
            console.log(`✅ [${itemIndex}] Success: ${tenderData.title?.substring(0, 30)}...`);
            
          } catch (error) {
            const errorMsg = `Failed to process item ${itemIndex} (${item.id}): ${error}`;
            errors.push(errorMsg);
            console.log(`❌ [${itemIndex}] ${errorMsg}`);
          }
          
          // 延遲避免被封
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // 批次間延遲
        if (i + BATCH_SIZE < limitedItems.length) {
          console.log(`⏳ Batch ${batchNumber} completed, waiting 2s...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      
      // 步驟 5: 保存結果
      const finalData = {
        sourceFile: "114-06-26.html.gz",
        processedAt: new Date().toISOString(),
        itemCount: limitedItems.length,
        tenderCount: results.length,
        successRate: `${((results.length / limitedItems.length) * 100).toFixed(1)}%`,
        items: limitedItems,
        tenders: results,
        errors
      };
      
      await FileManager.writeJsonFile(outputFile, finalData);
      console.log(`💾 Step 5: Results saved to ${outputFile}`);
      
      // 驗證最終結果
      expect(results.length + errors.length).toBe(limitedItems.length);
      expect(results.length).toBeGreaterThan(0);
      expect(errors.length).toBeGreaterThan(0); // 應該有模擬的錯誤
      expect(existsSync(outputFile)).toBe(true);
      
      console.log(`🎯 Complete workflow summary:`);
      console.log(`   Total items processed: ${limitedItems.length}`);
      console.log(`   Successful: ${results.length}`);
      console.log(`   Failed: ${errors.length}`);
      console.log(`   Success rate: ${finalData.successRate}`);
      console.log(`   Output file: ${outputFile}`);
      
      // 驗證輸出檔案內容
      const savedData = JSON.parse(readFileSync(outputFile, "utf-8"));
      expect(savedData.itemCount).toBe(limitedItems.length);
      expect(savedData.tenderCount).toBe(results.length);
      expect(savedData.tenders.length).toBe(results.length);
      
      console.log("✅ Complete workflow integration test passed!");
    }, timeout);
  });
});
