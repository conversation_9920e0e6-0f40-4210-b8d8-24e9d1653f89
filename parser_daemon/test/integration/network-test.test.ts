import { describe, it, expect, beforeAll } from "@jest/globals";
import { NetworkManager } from "../../src/core/NetworkManager";
import { PCCParser } from "../../src/sites/pcc";
import { Logger } from "../../src/core/utils";

describe("Network Connection Test", () => {
  let networkManager: NetworkManager;
  let parser: PCCParser;
  const timeout = 30000; // 30-second timeout

  beforeAll(() => {
    // 啟用詳細日誌
    Logger.setVerbose(true);
    networkManager = new NetworkManager(1);
    parser = new PCCParser();
    console.log("🌐 Starting network connection test");
  });

  describe("Basic Network Connectivity", () => {
    it(
      "should test basic internet connection",
      async () => {
        try {
          console.log("🔍 Testing basic internet connection...");
          const isConnected = await networkManager.testConnection();
          
          console.log(`📊 Basic connection result: ${isConnected ? '✅ Connected' : '❌ Failed'}`);
          
          // 這個測試不應該失敗，除非完全沒有網路
          if (!isConnected) {
            console.warn("⚠️ Basic internet connection failed - this may indicate network issues");
          }
          
          // 不管結果如何都通過測試，因為這只是診斷
          expect(true).toBe(true);
        } catch (error) {
          console.warn("⚠️ Basic connection test threw error:", error);
          expect(true).toBe(true);
        }
      },
      timeout
    );

    it(
      "should test government website connection",
      async () => {
        try {
          console.log("🏛️ Testing government website connection...");
          const isConnected = await parser.testConnection();
          
          console.log(`📊 Government website result: ${isConnected ? '✅ Connected' : '❌ Failed'}`);
          
          if (!isConnected) {
            console.warn("⚠️ Government website connection failed - this may be due to anti-crawling measures");
          }
          
          // 不管結果如何都通過測試，因為這只是診斷
          expect(true).toBe(true);
        } catch (error) {
          console.warn("⚠️ Government website test threw error:", error);
          expect(true).toBe(true);
        }
      },
      timeout
    );
  });

  describe("Fetch Implementation Test", () => {
    it(
      "should test fetch with simple URL",
      async () => {
        try {
          console.log("🧪 Testing fetch implementation with simple URL...");
          
          const result = await networkManager.request({
            url: "https://httpbin.org/get",
            method: "GET",
            timeout: 10000,
            retries: 1,
          });

          console.log(`📊 Fetch test result:`, {
            success: result.success,
            hasData: !!result.data,
            statusCode: result.statusCode,
            error: result.error,
          });

          if (result.success && result.data) {
            console.log("✅ Fetch implementation is working correctly");
            expect(result.success).toBe(true);
            expect(result.data).toBeDefined();
          } else {
            console.warn("⚠️ Fetch implementation may have issues:", result.error);
            expect(true).toBe(true); // 仍然通過測試
          }
        } catch (error) {
          console.warn("⚠️ Fetch test threw error:", error);
          expect(true).toBe(true);
        }
      },
      timeout
    );

    it(
      "should diagnose fetch behavior with detailed logging",
      async () => {
        try {
          console.log("🔬 Diagnosing fetch behavior...");
          
          // 測試一個簡單的 HEAD 請求
          const testUrl = "https://www.google.com";
          console.log(`Making HEAD request to: ${testUrl}`);
          
          const response = await fetch(testUrl, {
            method: "HEAD",
            headers: {
              "User-Agent": "Mozilla/5.0 (compatible; TestBot/1.0)",
            },
          });

          console.log("📊 Raw fetch result:", {
            responseExists: !!response,
            responseType: typeof response,
            hasOkProperty: response && 'ok' in response,
            okValue: response?.ok,
            status: response?.status,
            statusText: response?.statusText,
          });

          if (response) {
            console.log("✅ Fetch returns valid response object");
          } else {
            console.log("❌ Fetch returns undefined - this is the root cause of our issue");
          }

          expect(true).toBe(true);
        } catch (error) {
          console.log("❌ Fetch threw error:", error);
          expect(true).toBe(true);
        }
      },
      timeout
    );
  });

  describe("Environment Diagnostics", () => {
    it("should check runtime environment", () => {
      console.log("🔍 Runtime environment diagnostics:");
      console.log("- Runtime:", typeof (globalThis as any).Bun !== 'undefined' ? 'Bun' : 'Node.js');
      console.log("- Fetch available:", typeof fetch !== 'undefined');
      console.log("- AbortController available:", typeof AbortController !== 'undefined');
      console.log("- AbortSignal.timeout available:", typeof AbortSignal?.timeout !== 'undefined');
      
      expect(typeof fetch).toBe('function');
    });
  });
});
