import { NetworkManager } from "../../src/core/NetworkManager";

describe("URL 探索測試", () => {
  let networkManager: NetworkManager;

  beforeAll(async () => {
    networkManager = new NetworkManager(1);
  });

  test("測試不同的政府採購網URL", async () => {
    console.log('🔍 開始探索政府採購網的URL結構...');

    const testUrls = [
      // 原始的readPublish URL
      "https://web.pcc.gov.tw/prkms/tender/common/noticeDate/readPublish?dateStr=114年06月25日",
      
      // 標案查詢頁面
      "https://web.pcc.gov.tw/tps/pss/tender.do?searchMode=common&searchType=basic&searchTarget=ATM",
      
      // 得標公告查詢
      "https://web.pcc.gov.tw/tps/main/pms/tps/atm/atmAwardAction.do?newEdit=false&searchMode=common&searchType=basic&searchTarget=ATM",
      
      // 主頁面
      "https://web.pcc.gov.tw/",
      
      // 用戶提供的正確URL格式示例
      "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetailHist?fkPmsMainHist=NjgxNDMxNzE="
    ];

    for (let i = 0; i < testUrls.length; i++) {
      const url = testUrls[i];
      console.log(`\n📋 測試 URL ${i + 1}: ${url}`);
      
      try {
        const result = await networkManager.request({
          url: url,
          method: "GET",
          timeout: 30000,
          retries: 2,
        });

        if (result.success && result.data) {
          console.log(`✅ 成功，HTML 長度: ${result.data.length}`);
          
          // 檢查關鍵字
          const keywords = ['標案名稱', '機關名稱', '得標廠商', 'AtmAward', '.xml', '招標', '決標'];
          const foundKeywords = keywords.filter(keyword => result.data!.includes(keyword));
          console.log(`   包含關鍵字: ${foundKeywords.join(', ')} (${foundKeywords.length}/${keywords.length})`);
          
          // 檢查連結
          const cheerio = await import("cheerio");
          const $ = cheerio.load(result.data!);
          console.log(`   連結數量: ${$('a[href]').length}`);
          console.log(`   AtmAward連結: ${$('a[href*="AtmAward"]').length}`);
          console.log(`   XML連結: ${$('a[href*=".xml"]').length}`);

          // 顯示HTML預覽
          console.log(`   HTML預覽: ${result.data!.substring(0, 200)}...`);
          
        } else {
          console.log(`❌ 失敗: ${result.error}`);
        }
      } catch (error) {
        console.log(`❌ 異常: ${error}`);
      }
      
      // 延遲避免被封鎖
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 測試通過
    expect(true).toBe(true);
  }, 120000); // 2分鐘超時
});
