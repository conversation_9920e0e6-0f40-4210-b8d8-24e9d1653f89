import { BaseParser } from "../../core/Parser";
import { TenderData } from "../../../../shared/types";
import { Logger } from "../../core/utils";
import { FileManager } from "../../core/FileManager";
import { NetworkManager } from "../../core/NetworkManager";
import { CaptchaHandler } from "../../core/CaptchaHandler";

// Temporary ListItem interface for compatibility
interface ListItem {
  id: string;
  url: string;
  title: string;
  publishDate: string;
  agency: string;
}

export class PCCParser extends BaseParser {
  name = "pcc";

  private networkManager: NetworkManager;
  private captchaHandler: CaptchaHandler;

  constructor() {
    super();

    // 从环境变量获取网络接口数量，默认为1
    const interfaceCount = parseInt(process.env.INTERFACE_COUNT || "1");
    this.networkManager = new NetworkManager(interfaceCount);
    this.captchaHandler = new CaptchaHandler();

    Logger.info(
      `PCC Parser initialized with ${interfaceCount} network interfaces`
    );
  }

  // 实现基类抽象方法 - 暂时移除旧的解析逻辑
  async parseList(html: string, filename: string): Promise<ListItem[]> {
    // TODO: 使用 TDD 方式重新实现
    throw new Error("parseList method needs to be reimplemented using TDD");
  }

  async parseDetail(html: string, url: string): Promise<TenderData> {
    const cheerio = await import("cheerio");
    const $ = cheerio.load(html);

    // 提取基本資訊
    const title = this.extractTitle($);
    const tenderId = this.extractTenderId($);
    const agencyName = this.extractAgencyName($);
    const budgetAmount = this.extractBudgetAmount($);
    const category = this.extractCategory($);
    const announcementDate = this.extractAnnouncementDate($);
    const submissionDeadline = this.extractSubmissionDeadline($);
    const openingTime = this.extractOpeningTime($);

    // 創建基本的 TenderData 物件
    const tenderData: TenderData = {
      // Primary Identifiers
      tender_id: tenderId,
      case_number: tenderId, // 使用相同的值
      project_id: tenderId,   // 使用相同的值
      pcc_main_key: tenderId, // 使用相同的值

      // Basic Information
      title: title,
      description: title, // 暫時使用標題作為描述
      category: category,
      category_code: "", // 待實現
      category_type: this.determineCategoryType(category),

      // Agency Information
      agency_code: "", // 待實現
      agency_name: agencyName,
      agency_unit: "", // 待實現
      agency_address: "", // 待實現
      contact_person: "", // 待實現
      contact_phone: "", // 待實現
      contact_fax: "", // 待實現
      contact_email: "", // 待實現

      // Financial Information
      budget_amount: budgetAmount,
      budget_disclosed: budgetAmount > 0,
      budget_range: "", // 待實現

      // Dates
      announcement_date: announcementDate,
      submission_deadline: submissionDeadline,
      opening_time: openingTime,

      // Status and Type
      status: "bidding",
      tender_type: "tender",
      procurement_method: "", // 待實現
      award_method: "", // 待實現

      // Location and Performance
      performance_location: "", // 待實現
      performance_location_detail: "", // 待實現
      performance_period: "", // 待實現

      // Compliance and Regulations
      is_wto_gpa: false,
      is_anztec: false,
      is_astep: false,
      is_multiple_award: false,
      is_joint_procurement: false,
      is_electronic_bidding: false,
      requires_deposit: false,

      // Technical Requirements
      is_turnkey: false,
      requires_engineer_cert: false,
      is_special_procurement: false,

      // Metadata
      ai_score: 5, // 預設值
      source_url: url,
      publish_date: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      data_version: "1.0",

      // Additional Flags
      is_government_subsidy: false,
      is_disaster_reconstruction: false,
      is_sensitive_security: false,
      is_published_gazette: false,

      // History and Tracking
      history: []
    };

    return tenderData;
  }

  // 提取標案名稱
  private extractTitle($: any): string {
    // 從表格中提取標案名稱
    const titleElement = $('td:contains("標案名稱")').next().find('#tenderNameText');
    if (titleElement.length > 0) {
      return titleElement.text().trim();
    }

    // 備用方法：從表格中直接查找
    const titleRow = $('td:contains("標案名稱")').next();
    if (titleRow.length > 0) {
      return titleRow.text().trim();
    }

    return "";
  }

  // 提取標案編號
  private extractTenderId($: any): string {
    // 從表格中提取標案案號
    const tenderIdElement = $('td:contains("標案案號")').next();
    if (tenderIdElement.length > 0) {
      return tenderIdElement.text().trim();
    }

    return "";
  }

  // 提取機關名稱
  private extractAgencyName($: any): string {
    // 從表格中提取機關名稱
    const agencyElement = $('td:contains("機關名稱")').next();
    if (agencyElement.length > 0) {
      return agencyElement.text().trim();
    }

    return "";
  }

  // 提取預算金額
  private extractBudgetAmount($: any): number {
    // 從表格中提取預算金額
    const budgetElement = $('td:contains("預算金額")').next();
    if (budgetElement.length > 0) {
      const budgetText = budgetElement.text().trim();
      // 移除逗號和"元"字，提取數字
      const budgetMatch = budgetText.match(/[\d,]+/);
      if (budgetMatch) {
        const budgetStr = budgetMatch[0].replace(/,/g, '');
        return parseInt(budgetStr, 10) || 0;
      }
    }

    return 0;
  }

  // 提取標的分類
  private extractCategory($: any): string {
    const categoryElement = $('td:contains("標的分類")').next();
    if (categoryElement.length > 0) {
      return categoryElement.text().trim();
    }

    return "";
  }

  // 提取公告日期
  private extractAnnouncementDate($: any): Date {
    // 從 JavaScript 變數中提取日期
    const scriptContent = $('script').text();
    const targetDateMatch = scriptContent.match(/var checkTargetDate = "([^"]+)"/);

    if (targetDateMatch) {
      const dateStr = targetDateMatch[1];
      return new Date(dateStr);
    }

    return new Date();
  }

  // 提取截止投標時間
  private extractSubmissionDeadline($: any): Date | undefined {
    const deadlineElement = $('#spdt');
    if (deadlineElement.length > 0) {
      const deadlineText = deadlineElement.text().trim();
      // 轉換民國年格式 "114/07/07 17:00" 為西元年
      const match = deadlineText.match(/(\d+)\/(\d+)\/(\d+)\s+(\d+):(\d+)/);
      if (match) {
        const [, year, month, day, hour, minute] = match;
        const westernYear = parseInt(year) + 1911;
        return new Date(westernYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
      }
    }

    return undefined;
  }

  // 提取開標時間
  private extractOpeningTime($: any): Date | undefined {
    const openingElement = $('#opdt');
    if (openingElement.length > 0) {
      // 檢查是否有 display: none 樣式
      const style = openingElement.attr('style');
      const isHidden = style && style.includes('display: none');

      if (!isHidden) {
        const openingText = openingElement.text().trim();
        if (openingText) {
          // 轉換民國年格式為西元年
          const match = openingText.match(/(\d+)\/(\d+)\/(\d+)\s+(\d+):(\d+)/);
          if (match) {
            const [, year, month, day, hour, minute] = match;
            const westernYear = parseInt(year) + 1911;
            return new Date(westernYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
          }
        }
      }
    }

    return undefined;
  }

  // 根據分類決定標案類型
  private determineCategoryType(category: string): "construction" | "goods" | "services" | "other" {
    if (category.includes("工程")) {
      return "construction";
    } else if (category.includes("財物") || category.includes("設備")) {
      return "goods";
    } else if (category.includes("勞務") || category.includes("服務")) {
      return "services";
    }

    return "other";
  }

  // PCC特定的HTML验证
  validateListHtml(html: string): boolean {
    return (
      super.validateListHtml(html) &&
      (html.includes("政府電子採購網") || html.includes("標案公告"))
    );
  }

  validateDetailHtml(html: string): boolean {
    return (
      super.validateDetailHtml(html) &&
      (html.includes("機關資料") ||
        html.includes("採購資料") ||
        html.includes("招標資料"))
    );
  }

  // 解析文件功能 - 需要重新实现
  async parseFile(filePath: string): Promise<TenderData[]> {
    // TODO: 使用 TDD 方式重新实现
    throw new Error("parseFile method needs to be reimplemented using TDD");
  }

  // 舊的轉換邏輯已移除 - 將使用 TDD 方式重新實現

  async fetchTenderDetail(tenderUrl: string): Promise<TenderData> {
    try {
      Logger.info(`Fetching tender detail from: ${tenderUrl}`);

      // 首先測試網路連接
      Logger.info("Testing network connection before fetching...");
      const connectionTest = await this.testConnection();
      if (!connectionTest) {
        throw new Error("Network connection test failed. This may be due to:\n" +
          "1. Internet connectivity issues\n" +
          "2. Government website anti-crawling measures\n" +
          "3. Fetch API implementation problems in current environment\n" +
          "Recommendation: Use local HTML file processing instead.");
      }
      Logger.info("Network connection test passed, proceeding with fetch...");

      // 使用 NetworkManager 抓取網頁內容
      const result = await this.networkManager.request({
        url: tenderUrl,
        method: "GET",
        timeout: 30000,
        retries: 3,
      });

      if (!result.success || !result.data) {
        throw new Error(`Failed to fetch tender detail: ${result.error || 'No data received'}\n` +
          "This is likely due to network issues or anti-crawling measures.\n" +
          "Consider downloading the HTML file manually and using parseDetail() directly.");
      }

      // 使用新的 parseDetail 方法解析資料
      const tenderData = await this.parseDetail(result.data, tenderUrl);

      Logger.info(`Successfully parsed tender: ${tenderData.tender_id}`);
      return tenderData;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      Logger.error(`Error fetching tender detail from ${tenderUrl}: ${errorMessage}`);
      throw error;
    }
  }

  // 批量抓取列表页面 - 需要重新實現
  async fetchTenderList(
    dateStr: string,
    published: boolean = true
  ): Promise<TenderData[]> {
    // TODO: 使用 TDD 方式重新實現
    throw new Error("fetchTenderList method needs to be reimplemented using TDD");
  }

  // 获取网络状态统计
  getNetworkStats() {
    return this.networkManager.getInterfaceStats();
  }

  // 测试网络连接
  async testConnection(): Promise<boolean> {
    try {
      const testUrl =
        "https://web.pcc.gov.tw/prkms/tender/common/noticeDate/readPublish?dateStr=114年01月01日";

      const result = await this.networkManager.request({
        url: testUrl,
        useProxy: false, // 不使用代理进行连接测试
        timeout: 20000, // 增加超时时间
        retries: 3, // 增加重试次数
      });

      Logger.info(`Connection test result: ${result.success}`);

      // 即使遇到验证码也认为连接成功
      if (result.success || result.hasCaptcha) {
        Logger.info(
          "Network connection OK (may have captcha, but that's normal)"
        );
        return true;
      }

      return false;
    } catch (error) {
      Logger.error(`Connection test failed: ${error}`);
      return false;
    }
  }
}
