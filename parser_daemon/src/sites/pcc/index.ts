import { BaseParser } from "../../core/Parser";
import { TenderData } from "../../../../shared/types";
import { Logger } from "../../core/utils";
import { FileManager } from "../../core/FileManager";
import { NetworkManager } from "../../core/NetworkManager";
import { CaptchaHandler } from "../../core/CaptchaHandler";

// Temporary ListItem interface for compatibility
interface ListItem {
  id: string;
  url: string;
  title: string;
  publishDate: string;
  agency: string;
}

export class PCCParser extends BaseParser {
  name = "pcc";

  private networkManager: NetworkManager;
  private captchaHandler: CaptchaHandler;

  constructor() {
    super();

    // 从环境变量获取网络接口数量，默认为1
    const interfaceCount = parseInt(process.env.INTERFACE_COUNT || "1");
    this.networkManager = new NetworkManager(interfaceCount);
    this.captchaHandler = new CaptchaHandler();

    Logger.info(
      `PCC Parser initialized with ${interfaceCount} network interfaces`
    );
  }

  // 实现基类抽象方法 - 解析列表頁面
  async parseList(html: string, filename: string): Promise<ListItem[]> {
    const cheerio = await import("cheerio");
    const $ = cheerio.load(html);

    const items: ListItem[] = [];

    // 基於文字內容查找標案連結，支持多種格式
    // 1. 查找AtmAward格式的連結
    $('a[href*="AtmAward"]').each((index, element) => {
      const $link = $(element);
      const href = $link.attr('href');
      const linkText = $link.text().trim();

      if (href) {
        const id = `pcc_${Date.now()}_${this.generateRandomId()}`;
        const item: ListItem = {
          id: id,
          url: this.buildDetailUrl(href),
          title: linkText || "標案詳情",
          publishDate: "",
          agency: "政府機關"
        };
        items.push(item);
      }
    });

    // 2. 查找XML檔案格式的連結
    $('a[href*=".xml"]').each((index, element) => {
      const $link = $(element);
      const href = $link.attr('href');
      const linkText = $link.text().trim();

      if (href) {
        const id = `pcc_${Date.now()}_${this.generateRandomId()}`;
        const item: ListItem = {
          id: id,
          url: this.buildDetailUrl(href),
          title: linkText || "標案詳情",
          publishDate: "",
          agency: "政府機關"
        };
        items.push(item);
      }
    });

    // 3. 查找包含標案資訊的一般連結
    $('a[href]').each((index, element) => {
      const $link = $(element);
      const href = $link.attr('href');
      const linkText = $link.text().trim();

      // 跳過已經處理過的連結
      if (!href || !linkText || href.includes('AtmAward') || href.includes('.xml')) return;

      // 檢查是否為標案連結：包含機關名稱和標案編號的格式
      const tenderLinkPattern = /^(?:<(\d+)>\s*)?([^：]+)：([^-\s]+)\s*-\s*(.+)$/;
      const match = linkText.match(tenderLinkPattern);

      if (match) {
        const [, sequence, agency, tenderNumber, title] = match;
        const id = `pcc_${Date.now()}_${this.generateRandomId()}`;
        const item: ListItem = {
          id: id,
          url: this.buildDetailUrl(href),
          title: title.trim(),
          publishDate: "",
          agency: agency.trim()
        };
        items.push(item);
      }
    });

    // 如果沒有找到標案連結，嘗試查找表格中的文字內容
    if (items.length === 0) {
      $('table tr').each((index, element) => {
        const $row = $(element);
        const rowText = $row.text().trim();

        // 檢查行文字是否包含標案資訊
        const tenderPattern = /([^：]+)：([^-\s]+)\s*-\s*(.+)/;
        const match = rowText.match(tenderPattern);

        if (match) {
          const [, agency, tenderNumber, title] = match;

          // 查找該行中的連結
          const $link = $row.find('a[href]').first();
          const href = $link.attr('href');

          if (href) {
            const id = `pcc_${Date.now()}_${this.generateRandomId()}`;
            const item: ListItem = {
              id: id,
              url: this.buildDetailUrl(href),
              title: title.trim(),
              publishDate: "",
              agency: agency.trim()
            };
            items.push(item);
          }
        }
      });
    }

    Logger.info(`Parsed ${items.length} tender items from ${filename}`);
    return items;
  }

  // 構建詳細頁面URL的輔助方法
  private buildDetailUrl(href: string): string {
    // 如果已經是完整URL，直接返回
    if (href.startsWith('http')) {
      return href;
    }

    // 如果是相對路徑，構建完整URL
    if (href.startsWith('/')) {
      return `https://web.pcc.gov.tw${href}`;
    }

    // 如果是XML檔案名，構建redirectPublic URL
    if (href.endsWith('.xml')) {
      // 從XML檔案名中提取日期信息
      const xmlMatch = href.match(/([A-Z]+-\d+-(\d+))\.xml$/);
      if (xmlMatch) {
        const [, , dateNum] = xmlMatch;
        // 嘗試從檔案名推斷日期格式
        const ds = this.extractDateFromXmlFilename(href);
        const baseUrl = "https://web.pcc.gov.tw/prkms/tender/common/noticeDate/redirectPublic";
        return `${baseUrl}?ds=${ds}&fn=${href}`;
      }
    }

    // 如果包含AtmAward相關信息，構建AtmAward URL
    if (href.includes('fkPmsMainHist') || href.includes('AtmAward')) {
      return href.startsWith('http') ? href : `https://web.pcc.gov.tw${href}`;
    }

    // 預設情況
    return href;
  }

  // 從XML檔案名中提取日期的輔助方法
  private extractDateFromXmlFilename(filename: string): string {
    // XML檔案名格式通常為：TIQ-1-70783103.xml
    // 嘗試從檔案名中的數字推斷日期
    const match = filename.match(/(\d{8})/);
    if (match) {
      return match[1];
    }

    // 如果無法從檔案名推斷，使用當前日期
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }

  // 檢查頁面是否包含有效的標案列表
  private isValidTenderListPage(html: string): boolean {
    // 檢查是否包含標案相關的關鍵字
    const tenderKeywords = [
      '標案名稱', '招標案件', '機關名稱', '招標機關',
      '得標廠商', '決標金額', '預算金額', '標案編號',
      'AtmAward', 'fkPmsMainHist', '.xml'
    ];

    const foundKeywords = tenderKeywords.filter(keyword =>
      html.includes(keyword)
    );

    // 檢查XML連結數量（這是列表頁面的重要指標）
    const cheerio = require("cheerio");
    const $ = cheerio.load(html);
    const xmlLinkCount = $('a[href*=".xml"]').length;

    Logger.debug(`Found keywords: ${foundKeywords.join(', ')} (${foundKeywords.length}/${tenderKeywords.length})`);
    Logger.debug(`XML links found: ${xmlLinkCount}`);

    // 如果包含XML連結或多個標案相關關鍵字，認為是有效的標案列表頁面
    return xmlLinkCount > 0 || foundKeywords.length >= 2;
  }

  // 生成隨機 ID 的輔助方法
  private generateRandomId(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  async parseDetail(html: string, url: string): Promise<TenderData> {
    const cheerio = await import("cheerio");
    const $ = cheerio.load(html);

    // 提取基本資訊
    const title = this.extractTitle($);
    const tenderId = this.extractTenderId($);
    const agencyName = this.extractAgencyName($);
    const budgetAmount = this.extractBudgetAmount($);
    const category = this.extractCategory($);
    const announcementDate = this.extractAnnouncementDate($);
    const submissionDeadline = this.extractSubmissionDeadline($);
    const openingTime = this.extractOpeningTime($);

    // 創建基本的 TenderData 物件
    const tenderData: TenderData = {
      // Primary Identifiers
      tender_id: tenderId,
      case_number: tenderId, // 使用相同的值
      project_id: tenderId,   // 使用相同的值
      pcc_main_key: tenderId, // 使用相同的值

      // Basic Information
      title: title,
      description: title, // 暫時使用標題作為描述
      category: category,
      category_code: "", // 待實現
      category_type: this.determineCategoryType(category),

      // Agency Information
      agency_code: "", // 待實現
      agency_name: agencyName,
      agency_unit: "", // 待實現
      agency_address: "", // 待實現
      contact_person: "", // 待實現
      contact_phone: "", // 待實現
      contact_fax: "", // 待實現
      contact_email: "", // 待實現

      // Financial Information
      budget_amount: budgetAmount,
      budget_disclosed: budgetAmount > 0,
      budget_range: "", // 待實現

      // Dates
      announcement_date: announcementDate,
      submission_deadline: submissionDeadline,
      opening_time: openingTime,

      // Status and Type
      status: "bidding",
      tender_type: "tender",
      procurement_method: "", // 待實現
      award_method: "", // 待實現

      // Location and Performance
      performance_location: "", // 待實現
      performance_location_detail: "", // 待實現
      performance_period: "", // 待實現

      // Compliance and Regulations
      is_wto_gpa: false,
      is_anztec: false,
      is_astep: false,
      is_multiple_award: false,
      is_joint_procurement: false,
      is_electronic_bidding: false,
      requires_deposit: false,

      // Technical Requirements
      is_turnkey: false,
      requires_engineer_cert: false,
      is_special_procurement: false,

      // Metadata
      ai_score: 5, // 預設值
      source_url: url,
      publish_date: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      data_version: "1.0",

      // Additional Flags
      is_government_subsidy: false,
      is_disaster_reconstruction: false,
      is_sensitive_security: false,
      is_published_gazette: false,

      // History and Tracking
      history: []
    };

    return tenderData;
  }

  // 提取標案名稱
  private extractTitle($: any): string {
    // 從表格中提取標案名稱
    const titleElement = $('td:contains("標案名稱")').next().find('#tenderNameText');
    if (titleElement.length > 0) {
      return titleElement.text().trim();
    }

    // 備用方法：從表格中直接查找
    const titleRow = $('td:contains("標案名稱")').next();
    if (titleRow.length > 0) {
      return titleRow.text().trim();
    }

    return "";
  }

  // 提取標案編號
  private extractTenderId($: any): string {
    // 從表格中提取標案案號
    const tenderIdElement = $('td:contains("標案案號")').next();
    if (tenderIdElement.length > 0) {
      return tenderIdElement.text().trim();
    }

    return "";
  }

  // 提取機關名稱
  private extractAgencyName($: any): string {
    // 從表格中提取機關名稱
    const agencyElement = $('td:contains("機關名稱")').next();
    if (agencyElement.length > 0) {
      return agencyElement.text().trim();
    }

    return "";
  }

  // 提取預算金額
  private extractBudgetAmount($: any): number {
    // 從表格中提取預算金額
    const budgetElement = $('td:contains("預算金額")').next();
    if (budgetElement.length > 0) {
      const budgetText = budgetElement.text().trim();
      // 移除逗號和"元"字，提取數字
      const budgetMatch = budgetText.match(/[\d,]+/);
      if (budgetMatch) {
        const budgetStr = budgetMatch[0].replace(/,/g, '');
        return parseInt(budgetStr, 10) || 0;
      }
    }

    return 0;
  }

  // 提取標的分類
  private extractCategory($: any): string {
    const categoryElement = $('td:contains("標的分類")').next();
    if (categoryElement.length > 0) {
      return categoryElement.text().trim();
    }

    return "";
  }

  // 提取公告日期
  private extractAnnouncementDate($: any): Date {
    // 從 JavaScript 變數中提取日期
    const scriptContent = $('script').text();
    const targetDateMatch = scriptContent.match(/var checkTargetDate = "([^"]+)"/);

    if (targetDateMatch) {
      const dateStr = targetDateMatch[1];
      return new Date(dateStr);
    }

    return new Date();
  }

  // 提取截止投標時間
  private extractSubmissionDeadline($: any): Date | undefined {
    const deadlineElement = $('#spdt');
    if (deadlineElement.length > 0) {
      const deadlineText = deadlineElement.text().trim();
      // 轉換民國年格式 "114/07/07 17:00" 為西元年
      const match = deadlineText.match(/(\d+)\/(\d+)\/(\d+)\s+(\d+):(\d+)/);
      if (match) {
        const [, year, month, day, hour, minute] = match;
        const westernYear = parseInt(year) + 1911;
        return new Date(westernYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
      }
    }

    return undefined;
  }

  // 提取開標時間
  private extractOpeningTime($: any): Date | undefined {
    const openingElement = $('#opdt');
    if (openingElement.length > 0) {
      // 檢查是否有 display: none 樣式
      const style = openingElement.attr('style');
      const isHidden = style && style.includes('display: none');

      if (!isHidden) {
        const openingText = openingElement.text().trim();
        if (openingText) {
          // 轉換民國年格式為西元年
          const match = openingText.match(/(\d+)\/(\d+)\/(\d+)\s+(\d+):(\d+)/);
          if (match) {
            const [, year, month, day, hour, minute] = match;
            const westernYear = parseInt(year) + 1911;
            return new Date(westernYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
          }
        }
      }
    }

    return undefined;
  }

  // 根據分類決定標案類型
  private determineCategoryType(category: string): "construction" | "goods" | "services" | "other" {
    if (category.includes("工程")) {
      return "construction";
    } else if (category.includes("財物") || category.includes("設備")) {
      return "goods";
    } else if (category.includes("勞務") || category.includes("服務")) {
      return "services";
    }

    return "other";
  }

  // PCC特定的HTML验证
  validateListHtml(html: string): boolean {
    return (
      super.validateListHtml(html) &&
      (html.includes("政府電子採購網") || html.includes("標案公告"))
    );
  }

  validateDetailHtml(html: string): boolean {
    return (
      super.validateDetailHtml(html) &&
      (html.includes("機關資料") ||
        html.includes("採購資料") ||
        html.includes("招標資料"))
    );
  }

  // 解析文件功能 - 需要重新实现
  async parseFile(filePath: string): Promise<TenderData[]> {
    // TODO: 使用 TDD 方式重新实现
    throw new Error("parseFile method needs to be reimplemented using TDD");
  }

  // 舊的轉換邏輯已移除 - 將使用 TDD 方式重新實現

  async fetchTenderDetail(tenderUrl: string): Promise<TenderData> {
    try {
      Logger.info(`Fetching tender detail from: ${tenderUrl}`);

      // 首先測試網路連接
      Logger.info("Testing network connection before fetching...");
      const connectionTest = await this.testConnection();
      if (!connectionTest) {
        throw new Error("Network connection test failed. This may be due to:\n" +
          "1. Internet connectivity issues\n" +
          "2. Government website anti-crawling measures\n" +
          "3. Fetch API implementation problems in current environment\n" +
          "Recommendation: Use local HTML file processing instead.");
      }
      Logger.info("Network connection test passed, proceeding with fetch...");

      // 使用 NetworkManager 抓取網頁內容
      const result = await this.networkManager.request({
        url: tenderUrl,
        method: "GET",
        timeout: 30000,
        retries: 3,
      });

      if (!result.success || !result.data) {
        throw new Error(`Failed to fetch tender detail: ${result.error || 'No data received'}\n` +
          "This is likely due to network issues or anti-crawling measures.\n" +
          "Consider downloading the HTML file manually and using parseDetail() directly.");
      }

      // 使用新的 parseDetail 方法解析資料
      const tenderData = await this.parseDetail(result.data, tenderUrl);

      Logger.info(`Successfully parsed tender: ${tenderData.tender_id}`);
      return tenderData;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      Logger.error(`Error fetching tender detail from ${tenderUrl}: ${errorMessage}`);
      throw error;
    }
  }

  // 批量抓取列表页面
  async fetchTenderList(
    dateStr: string,
    published: boolean = true,
    maxResults: number = 50
  ): Promise<TenderData[]> {
    try {
      Logger.info(`Fetching tender list for date: ${dateStr}, max results: ${maxResults}`);

      // 轉換日期格式：從 "114年06月25日" 轉換為 "20250625"
      const isoDate = this.convertRocDateToIso(dateStr);
      Logger.debug(`Converted date from ${dateStr} to ${isoDate}`);

      // 構建列表頁面 URL - 嘗試不同的URL格式來找到真正的標案列表
      const possibleUrls = [
        // 嘗試標案查詢頁面
        `https://web.pcc.gov.tw/tps/pss/tender.do?searchMode=common&searchType=basic&searchTarget=ATM&orgName=&orgId=&hid_1=1&tenderName=&tenderId=&tenderStatus=&tenderWay=&awardAnnounceStartDate=${isoDate}&awardAnnounceEndDate=${isoDate}&radProctrgCate=&proctrgCate=&minBudget=&maxBudget=`,
        // 嘗試得標公告查詢
        `https://web.pcc.gov.tw/tps/main/pms/tps/atm/atmAwardAction.do?newEdit=false&searchMode=common&searchType=basic&searchTarget=ATM&orgName=&orgId=&hid_1=1&tenderName=&tenderId=&tenderStatus=&tenderWay=&awardAnnounceStartDate=${isoDate}&awardAnnounceEndDate=${isoDate}`,
        // 原始的readPublish URL作為備用
        `https://web.pcc.gov.tw/prkms/tender/common/noticeDate/readPublish?dateStr=${encodeURIComponent(dateStr)}`
      ];

      let listUrl = possibleUrls[0]; // 預設使用第一個URL
      Logger.debug(`Trying to fetch list from: ${listUrl}`);

      // 嘗試抓取列表頁面，依次嘗試不同的URL
      let result: any = null;
      let successfulUrl = "";

      for (const url of possibleUrls) {
        Logger.debug(`Trying URL: ${url}`);

        const attemptResult = await this.networkManager.request({
          url: url,
          method: "GET",
          timeout: 30000,
          retries: 2,
        });

        if (attemptResult.success && attemptResult.data) {
          // 檢查返回的內容是否包含標案相關信息
          const hasRelevantContent = this.isValidTenderListPage(attemptResult.data);

          if (hasRelevantContent) {
            result = attemptResult;
            successfulUrl = url;
            Logger.info(`Successfully found tender list at: ${url}`);
            break;
          } else {
            Logger.debug(`URL ${url} returned content but no tender data found`);
          }
        } else {
          Logger.debug(`URL ${url} failed: ${attemptResult.error}`);
        }

        // 延遲避免被封鎖
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      if (!result || !result.success || !result.data) {
        throw new Error(`Failed to fetch tender list from all attempted URLs`);
      }

      // 解析列表頁面，提取 ListItem
      const listItems = await this.parseList(result.data, `list-${isoDate}.html`);
      Logger.info(`Found ${listItems.length} tender items in list`);

      // 限制結果數量
      const limitedItems = listItems.slice(0, maxResults);
      Logger.info(`Processing ${limitedItems.length} items (limited from ${listItems.length})`);

      // 批量抓取詳細資料
      const tenderDataList: TenderData[] = [];
      const errors: string[] = [];

      for (let i = 0; i < limitedItems.length; i++) {
        const item = limitedItems[i];

        try {
          Logger.debug(`Processing item ${i + 1}/${limitedItems.length}: ${item.title}`);

          // 抓取詳細資料
          const tenderData = await this.fetchTenderDetail(item.url);

          // 補充基本資訊
          if (item.id) tenderData.tender_id = item.id;
          if (item.title) tenderData.title = item.title;
          if (item.agency) tenderData.agency_name = item.agency;

          tenderDataList.push(tenderData);
          Logger.debug(`Successfully processed item ${i + 1}: ${tenderData.tender_id}`);

          // 添加延遲避免被封
          if (i < limitedItems.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }

        } catch (error) {
          const errorMsg = `Failed to process item ${item.id}: ${error}`;
          Logger.warn(errorMsg);
          errors.push(errorMsg);

          // 創建 fallback 資料
          const fallbackData: TenderData = {
            tender_id: item.id || `fallback_${i}`,
            case_number: item.id || `fallback_${i}`,
            project_id: item.id || `fallback_${i}`,
            pcc_main_key: item.id || `fallback_${i}`,
            title: item.title || "Unknown Title",
            description: "Fallback data due to crawl failure",
            category: "其他",
            category_code: "99",
            category_type: "other" as const,
            agency_code: "unknown",
            agency_name: item.agency || "Unknown Agency",
            agency_unit: "",
            agency_address: "",
            contact_person: "",
            contact_phone: "",
            contact_fax: "",
            contact_email: "",
            budget_amount: 0,
            budget_disclosed: false,
            budget_range: "",
            announcement_date: new Date(),
            submission_deadline: new Date(),
            opening_time: new Date(),
            status: "open",
            tender_type: "tender",
            procurement_method: "",
            award_method: "",
            performance_location: "",
            performance_location_detail: "",
            performance_period: "",
            is_wto_gpa: false,
            is_anztec: false,
            is_astep: false,
            is_multiple_award: false,
            is_joint_procurement: false,
            is_electronic_bidding: false,
            requires_deposit: false,
            is_turnkey: false,
            requires_engineer_cert: false,
            is_special_procurement: false,
            ai_score: 0,
            source_url: item.url,
            publish_date: new Date(),
            created_at: new Date(),
            updated_at: new Date(),
            data_version: "1.0",
            is_government_subsidy: false,
            is_disaster_reconstruction: false,
            is_sensitive_security: false,
            is_published_gazette: false,
            history: []
          };

          tenderDataList.push(fallbackData);
        }
      }

      Logger.info(`Successfully processed ${tenderDataList.length} tenders, ${errors.length} errors`);

      if (errors.length > 0) {
        Logger.warn(`Errors encountered: ${errors.join('; ')}`);
      }

      return tenderDataList;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      Logger.error(`Error fetching tender list for ${dateStr}: ${errorMessage}`);
      throw error;
    }
  }

  // 轉換民國年日期格式為 ISO 格式
  private convertRocDateToIso(rocDate: string): string {
    // 解析 "114年06月25日" 格式
    const match = rocDate.match(/^(\d+)年(\d+)月(\d+)日$/);
    if (!match) {
      throw new Error(`Invalid ROC date format: ${rocDate}`);
    }

    const rocYear = parseInt(match[1]);
    const month = match[2].padStart(2, '0');
    const day = match[3].padStart(2, '0');

    // 轉換為西元年
    const adYear = rocYear + 1911;

    return `${adYear}${month}${day}`;
  }

  // 获取网络状态统计
  getNetworkStats() {
    return this.networkManager.getInterfaceStats();
  }

  // 测试网络连接
  async testConnection(): Promise<boolean> {
    try {
      const testUrl =
        "https://web.pcc.gov.tw/prkms/tender/common/noticeDate/readPublish?dateStr=114年01月01日";

      const result = await this.networkManager.request({
        url: testUrl,
        useProxy: false, // 不使用代理进行连接测试
        timeout: 20000, // 增加超时时间
        retries: 3, // 增加重试次数
      });

      Logger.info(`Connection test result: ${result.success}`);

      // 即使遇到验证码也认为连接成功
      if (result.success || result.hasCaptcha) {
        Logger.info(
          "Network connection OK (may have captcha, but that's normal)"
        );
        return true;
      }

      return false;
    } catch (error) {
      Logger.error(`Connection test failed: ${error}`);
      return false;
    }
  }
}
