// API Response Types
export interface ListByDateResponse {
  records: ListRecord[];
}

export interface ListRecord {
  date: number;
  filename: string;
  brief: {
    type: string;
    title: string;
    category?: string;
    companies: {
      ids: string[];
      names: string[];
      id_key: Record<string, string[]>;
      name_key: Record<string, string[]>;
    };
  };
  job_number: string;
  unit_id: string;
  unit_name: string;
  unit_api_url: string;
  tender_api_url: string;
  unit_url: string;
  url: string;
}

export interface TenderDetailResponse {
  unit_name: string;
  records: TenderRecord[];
}

export interface TenderRecord {
  date: number;
  filename: string;
  brief: {
    type: string;
    title: string;
    companies: {
      ids: string[];
      names: string[];
      id_key: Record<string, string[]>;
      name_key: Record<string, string[]>;
    };
  };
  job_number: string;
  unit_id: string;
  detail: {
    type: string;
    url: string;
    機關資料: {
      機關代碼: string;
      機關名稱: string;
      單位名稱: string;
      機關地址: string;
      聯絡人: string;
      聯絡電話: string;
      傳真號碼: string;
    };
    已公告資料: {
      標案案號: string;
      招標方式: string;
      決標方式: string;
      是否依政府採購法施行細則第64條之2辦理: string;
      新增公告傳輸次數: string;
      是否依據採購法第106條第1項第1款辦理: string;
      標案名稱: string;
      決標資料類別: string;
      是否屬共同供應契約採購: string;
      是否屬二以上機關之聯合採購: string;
      是否複數決標: string;
      是否共同投標: string;
      標的分類: string;
      是否屬統包: string;
      是否應依公共工程專業技師簽證規則實施技師簽證: string;
      開標時間: string;
      原公告日期: string;
      採購金額級距: string;
      辦理方式: string;
      是否適用條約或協定之採購: {
        是否適用WTO政府採購協定: string;
        是否適用臺紐經濟合作協定: string;
        是否適用臺星經濟夥伴協定: string;
      };
      預算金額是否公開: string;
      預算金額: string;
      是否受機關補助: string;
      履約地點: string;
      履約地點含地區: string;
      是否含特別預算: string;
      歸屬計畫類別: string;
      本案採購契約是否採用主管機關訂定之範本: string;
      是否屬災區重建工程: string;
    };
    投標廠商: {
      投標廠商家數: string;
      [key: string]: any; // For dynamic vendor entries
    };
    決標品項: {
      決標品項數: string;
      [key: string]: any; // For dynamic item entries
    };
    決標資料: {
      決標公告序號: string;
      決標日期: string;
      決標公告日期: string;
      是否刊登公報: string;
      底價金額: string;
      底價金額是否公開: string;
      總決標金額: string;
      總決標金額是否公開: string;
      契約是否訂有依物價指數調整價金規定: string;
      未列物價調整規定說明?: string;
      履約執行機關: string;
      附加說明?: string;
    };
    fetched_at: string;
  };
  unit_name: string;
  unit_api_url: string;
  tender_api_url: string;
  unit_url: string;
  url: string;
}

// Progress Tracking
export interface ProgressData {
  startDate: string;
  endDate: string;
  totalItems: number;
  completedItems: number;
  failedItems: number;
  processedIds: Set<string>;
  pendingIds: Set<string>;
  lastProcessedDate: string;
  startTime: number;
  lastUpdateTime: number;
}

export interface FailedItem {
  unit_id: string;
  job_number: string;
  date: number;
  error: string;
  retryCount: number;
  lastAttempt: number;
}

// Configuration
export interface Config {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    delayBetweenRequests: number;
  };
  processing: {
    batchSize: number;
    concurrency: number;
    progressSaveInterval: number;
  };
  storage: {
    outputDir: string;
    progressFile: string;
    failedFile: string;
  };
}

/// ------------------------------------------------------------

// TenderData interface - Optimized for PostgreSQL database and analytics
export interface TenderData {
  // === Primary Identifiers ===
  tender_id: string; // 標案編號 - Primary key
  case_number: string; // 標案案號 - Government case number
  project_id: string; // 專案編號 - Internal project ID
  pcc_main_key: string; // 政府電子採購網主鍵

  // === Basic Information ===
  title: string; // 標案名稱
  description: string; // 標案描述
  category: string; // 標的分類
  category_code: string; // 標的分類代碼 (extracted from category)
  category_type: "goods" | "services" | "construction" | "other"; // 採購類型

  // === Agency Information ===
  agency_code: string; // 機關代碼
  agency_name: string; // 機關名稱
  agency_unit: string; // 單位名稱
  agency_address: string; // 機關地址
  contact_person: string; // 聯絡人
  contact_phone: string; // 聯絡電話
  contact_fax: string; // 傳真號碼
  contact_email: string; // 電子郵件

  // === Financial Information ===
  budget_amount: number; // 預算金額
  budget_disclosed: boolean; // 預算金額是否公開
  budget_range: string; // 採購金額級距
  award_amount?: number; // 決標金額
  award_disclosed?: boolean; // 決標金額是否公開
  reserve_price?: number; // 底價
  reserve_disclosed?: boolean; // 底價是否公開

  // === Dates ===
  announcement_date: Date; // 公告日期
  original_announcement_date?: Date; // 原公告日期
  submission_deadline?: Date; // 截止投標
  opening_time?: Date; // 開標時間
  award_date?: Date; // 決標日期

  // === Status and Type ===
  status: "bidding" | "awarded" | "failed" | "cancelled" | "open" | "closed";
  tender_type: "tender" | "award" | "failure" | "amendment" | "cancellation";
  procurement_method: string; // 招標方式
  award_method: string; // 決標方式

  // === Location and Performance ===
  performance_location: string; // 履約地點
  performance_location_detail: string; // 履約地點詳細
  performance_period: string; // 履約期限

  // === Compliance and Regulations ===
  is_wto_gpa: boolean; // 是否適用WTO政府採購協定
  is_anztec: boolean; // 是否適用臺紐經濟合作協定
  is_astep: boolean; // 是否適用臺星經濟夥伴協定
  is_multiple_award: boolean; // 是否複數決標
  is_joint_procurement: boolean; // 是否聯合採購
  is_electronic_bidding: boolean; // 是否提供電子投標
  requires_deposit: boolean; // 是否須繳納押標金
  deposit_amount?: number; // 押標金額度

  // === Technical Requirements ===
  is_turnkey: boolean; // 是否屬統包
  requires_engineer_cert: boolean; // 是否需要技師簽證
  is_special_procurement: boolean; // 是否屬特殊採購

  // === Metadata ===
  ai_score: number; // AI評分 (1-10)
  source_url: string; // 原始網址
  publish_date: Date; // 發布日期 (for file organization)
  created_at: Date; // 記錄創建時間
  updated_at: Date; // 記錄更新時間
  data_version: string; // 數據版本

  // === Bidding Information ===
  bidder_count?: number; // 投標廠商家數
  document_fee?: number; // 文件費用

  // === Additional Flags ===
  is_government_subsidy: boolean; // 是否受機關補助
  is_disaster_reconstruction: boolean; // 是否災區重建工程
  is_sensitive_security: boolean; // 是否涉及國安疑慮
  is_published_gazette: boolean; // 是否刊登公報

  // === History and Tracking ===
  history: HistoryRecord[]; // 歷史記錄

  // === Related Data (for complex queries) ===
  bidders?: TenderBidder[]; // 投標廠商
  award_items?: TenderAwardItem[]; // 決標品項
}

// Supporting interfaces for TenderData
export interface TenderBidder {
  bidder_id: string;
  company_name: string;
  company_id: string;
  bid_amount?: number;
  is_winner: boolean;
  rank?: number;
}

export interface TenderAwardItem {
  item_id: string;
  item_name: string;
  quantity: number;
  unit: string;
  unit_price: number;
  total_amount: number;
  winner_company: string;
}

// History Record for tracking changes
export interface HistoryRecord {
  date: string; // ISO date string
  status: "bidding" | "awarded" | "failed" | "open" | "closed" | "updated";
  tenderType: "tender" | "award" | "failure" | "amendment" | "cancellation";
  announcementDate?: string;
  openingTime?: string;
  deadline?: string;
  awardDate?: string;
  failureDate?: string;
  changes?: string[]; // List of changed fields
  metadata?: {
    filename?: string;
    recordDate?: number;
    fetchedAt?: string;
    [key: string]: any;
  };
}
