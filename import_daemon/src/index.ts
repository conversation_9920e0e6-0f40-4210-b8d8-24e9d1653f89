#!/usr/bin/env node

// Load environment variables from .env file
import * as dotenv from "dotenv";
dotenv.config();

import { Command } from "commander";
import * as fs from "fs-extra";
import * as path from "path";
import { format, addDays, isValid } from "date-fns";
import { Fetcher } from "./fetcher";
import { Storage } from "./storage";
import { Transformer } from "./transformer";
import { Config, ProgressData, FailedItem, ListRecord } from "./types";

class ImportDaemon {
  private fetcher: Fetcher;
  private storage: Storage;
  private transformer: Transformer;
  private config!: Config;
  private progress: ProgressData;
  private failedItems: FailedItem[] = [];

  constructor(configPath: string) {
    this.loadConfig(configPath);
    this.fetcher = new Fetcher(this.config);
    this.storage = new Storage(this.config);
    this.transformer = new Transformer();
    this.progress = this.initializeProgress();
  }

  private loadConfig(configPath: string): void {
    try {
      const configData = fs.readFileSync(configPath, "utf8");
      this.config = JSON.parse(configData);
    } catch (error) {
      console.error("Failed to load config:", error);
      process.exit(1);
    }
  }

  private initializeProgress(): ProgressData {
    return {
      startDate: "",
      endDate: "",
      totalItems: 0,
      completedItems: 0,
      failedItems: 0,
      processedIds: new Set(),
      pendingIds: new Set(),
      lastProcessedDate: "",
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
    };
  }

  async import(
    startDate: string,
    endDate: string,
    retryOnly: boolean = false,
    limit: number = 0
  ): Promise<void> {
    try {
      console.log(`🚀 Starting import from ${startDate} to ${endDate}`);

      await this.storage.ensureDirectories();

      if (retryOnly) {
        await this.retryFailedItems();
        return;
      }

      // Load existing progress
      const existingProgress = await this.storage.loadProgress();
      if (
        existingProgress &&
        existingProgress.startDate === startDate &&
        existingProgress.endDate === endDate
      ) {
        this.progress = existingProgress;
        console.log(
          `📋 Resuming progress: ${this.progress.completedItems}/${this.progress.totalItems} completed`
        );
      } else {
        await this.initializeNewImport(startDate, endDate, limit);
      }

      // Load failed items
      this.failedItems = await this.storage.loadFailedItems();

      // Process pending items
      await this.processPendingItems();

      // Final report
      await this.generateFinalReport();
    } catch (error) {
      console.error("❌ Import failed:", error);
      await this.saveProgress();
      throw error;
    }
  }

  private async initializeNewImport(
    startDate: string,
    endDate: string,
    limit: number = 0
  ): Promise<void> {
    console.log("📊 Collecting tender list...");

    const allTenders: ListRecord[] = [];
    const currentDate = new Date(this.parseDate(startDate));
    const endDateObj = new Date(this.parseDate(endDate));

    // Collect all tenders in date range
    while (currentDate <= endDateObj) {
      const dateStr = format(currentDate, "yyyyMMdd");
      try {
        console.log(`📅 Fetching list for ${dateStr}`);
        const response = await this.fetcher.fetchListByDate(dateStr);
        allTenders.push(...response.records);
        console.log(
          `✓ Found ${response.records.length} tenders for ${dateStr}`
        );
      } catch (error) {
        console.warn(`⚠️ Failed to fetch list for ${dateStr}:`, error);
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Remove duplicates based on unit_id + job_number
    let uniqueTenders = this.removeDuplicates(allTenders);
    console.log(
      `📝 Total unique tenders: ${uniqueTenders.length} (removed ${
        allTenders.length - uniqueTenders.length
      } duplicates)`
    );

    // Apply limit if specified (for testing)
    if (limit > 0 && uniqueTenders.length > limit) {
      uniqueTenders = uniqueTenders.slice(0, limit);
      console.log(`🔬 Limited to ${limit} tenders for testing`);
    }

    // Initialize progress
    this.progress = {
      startDate,
      endDate,
      totalItems: uniqueTenders.length,
      completedItems: 0,
      failedItems: 0,
      processedIds: new Set(),
      pendingIds: new Set(
        uniqueTenders.map((t) =>
          this.storage.generateUniqueId(t.unit_id, t.job_number)
        )
      ),
      lastProcessedDate: startDate,
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
    };

    await this.saveProgress();
  }

  private removeDuplicates(tenders: ListRecord[]): ListRecord[] {
    const seen = new Set<string>();
    return tenders.filter((tender) => {
      const id = this.storage.generateUniqueId(
        tender.unit_id,
        tender.job_number
      );
      if (seen.has(id)) {
        return false;
      }
      seen.add(id);
      return true;
    });
  }

  private async processPendingItems(): Promise<void> {
    const pendingArray = Array.from(this.progress.pendingIds);
    console.log(`🔄 Processing ${pendingArray.length} pending items...`);

    // 將待處理項目轉換為批次
    const batchSize = this.config.processing.batchSize;
    const batches: Array<
      Array<{ unitId: string; jobNumber: string; tenderId: string }>
    > = [];

    for (let i = 0; i < pendingArray.length; i += batchSize) {
      const batch = pendingArray.slice(i, i + batchSize).map((tenderId) => {
        const [unitId, jobNumber] = tenderId.split("_");
        return { unitId, jobNumber, tenderId };
      });
      batches.push(batch);
    }

    console.log(
      `📦 Processing in ${batches.length} batches of up to ${batchSize} items each`
    );

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(
        `\n🔄 Processing batch ${batchIndex + 1}/${batches.length} (${
          batch.length
        } items)`
      );

      // 準備併發請求
      const concurrentTasks = batch.map((item) => ({
        unitId: item.unitId,
        jobNumber: item.jobNumber,
      }));

      try {
        // 併發抓取這個批次的所有項目
        const results = await this.fetcher.fetchTenderDetailsConcurrently(
          concurrentTasks
        );

        // 處理結果
        for (let i = 0; i < results.length; i++) {
          const result = results[i];
          const batchItem = batch[i];

          try {
            if (result.success && result.data) {
              // 轉換並保存成功的結果
              const tenderRecords = this.transformer.transformTenderDetail(
                result.data,
                this.createDummyListRecord(result.unitId, result.jobNumber)
              );

              // 使用新的合併邏輯處理多筆記錄
              if (tenderRecords.length > 0) {
                const cleanedRecords = tenderRecords.map((record) =>
                  this.transformer.cleanTenderRecord(record)
                );

                // 合併多筆記錄成一個 TenderData
                const tenderData =
                  this.transformer.mergeRecordsToTenderData(cleanedRecords);
                await this.storage.saveTenderData(tenderData);

                console.log(
                  `✓ [${i + 1}/${results.length}] ${
                    tenderData.project_id
                  } completed`
                );
              } else {
                console.warn(
                  `⚠️ No valid records found for ${result.unitId}_${result.jobNumber}`
                );
              }

              // 更新進度
              this.progress.processedIds.add(batchItem.tenderId);
              this.progress.pendingIds.delete(batchItem.tenderId);
              this.progress.completedItems++;
            } else {
              // 處理失敗的項目
              console.error(
                `❌ Failed to process ${batchItem.tenderId}:`,
                result.error?.message
              );

              const failedItem: FailedItem = {
                unit_id: result.unitId,
                job_number: result.jobNumber,
                date: Date.now(),
                error: result.error?.message || "Unknown error",
                retryCount: 0,
                lastAttempt: Date.now(),
              };

              this.failedItems.push(failedItem);
              this.progress.failedItems++;
              this.progress.pendingIds.delete(batchItem.tenderId);
            }
          } catch (processingError) {
            console.error(
              `❌ Error processing result for ${batchItem.tenderId}:`,
              processingError
            );

            const failedItem: FailedItem = {
              unit_id: result.unitId,
              job_number: result.jobNumber,
              date: Date.now(),
              error:
                processingError instanceof Error
                  ? processingError.message
                  : String(processingError),
              retryCount: 0,
              lastAttempt: Date.now(),
            };

            this.failedItems.push(failedItem);
            this.progress.failedItems++;
            this.progress.pendingIds.delete(batchItem.tenderId);
          }
        }

        // 更新進度時間戳
        this.progress.lastUpdateTime = Date.now();

        // 保存進度和失敗項目
        await this.saveProgress();
        if (this.failedItems.length > 0) {
          await this.storage.saveFailedItems(this.failedItems);
        }

        console.log(
          `✅ Batch ${batchIndex + 1} completed: ${
            this.progress.completedItems
          }/${this.progress.totalItems}`
        );

        // 顯示批次處理統計
        console.log(
          `📊 Batch processing: batchSize=${this.config.processing.batchSize}`
        );
      } catch (error) {
        console.error(`❌ Batch ${batchIndex + 1} failed:`, error);

        // 將整個批次標記為失敗
        for (const item of batch) {
          const failedItem: FailedItem = {
            unit_id: item.unitId,
            job_number: item.jobNumber,
            date: Date.now(),
            error: error instanceof Error ? error.message : String(error),
            retryCount: 0,
            lastAttempt: Date.now(),
          };

          this.failedItems.push(failedItem);
          this.progress.failedItems++;
          this.progress.pendingIds.delete(item.tenderId);
        }

        await this.storage.saveFailedItems(this.failedItems);
      }
    }

    await this.saveProgress();
  }

  async retryFailedItems(): Promise<void> {
    console.log("🔄 Retrying failed items...");

    this.failedItems = await this.storage.loadFailedItems();
    if (this.failedItems.length === 0) {
      console.log("✅ No failed items to retry");
      return;
    }

    console.log(`📋 Found ${this.failedItems.length} failed items to retry`);

    // 將失敗項目分批處理
    const batchSize = this.config.processing.batchSize;
    const batches: Array<Array<FailedItem>> = [];

    for (let i = 0; i < this.failedItems.length; i += batchSize) {
      const batch = this.failedItems.slice(i, i + batchSize);
      batches.push(batch);
    }

    console.log(
      `📦 Retrying in ${batches.length} batches of up to ${batchSize} items each`
    );

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(
        `\n🔄 Retry batch ${batchIndex + 1}/${batches.length} (${
          batch.length
        } items)`
      );

      // 準備併發請求
      const concurrentTasks = batch.map((item) => ({
        unitId: item.unit_id,
        jobNumber: item.job_number,
      }));

      try {
        // 併發重試這個批次的所有項目
        const results = await this.fetcher.fetchTenderDetailsConcurrently(
          concurrentTasks
        );

        // 處理結果
        for (let i = 0; i < results.length; i++) {
          const result = results[i];
          const failedItem = batch[i];

          try {
            if (result.success && result.data) {
              console.log(
                `✅ Successfully retried ${failedItem.unit_id}_${failedItem.job_number}`
              );

              // 轉換並保存成功的結果
              const tenderRecords = this.transformer.transformTenderDetail(
                result.data,
                this.createDummyListRecord(result.unitId, result.jobNumber)
              );

              // 使用新的合併邏輯處理多筆記錄
              if (tenderRecords.length > 0) {
                const cleanedRecords = tenderRecords.map((record) =>
                  this.transformer.cleanTenderRecord(record)
                );

                // 合併多筆記錄成一個 TenderData
                const tenderData =
                  this.transformer.mergeRecordsToTenderData(cleanedRecords);
                await this.storage.saveTenderData(tenderData);
              }

              // 從失敗列表中移除
              const itemIndex = this.failedItems.findIndex(
                (item) =>
                  item.unit_id === failedItem.unit_id &&
                  item.job_number === failedItem.job_number
              );
              if (itemIndex !== -1) {
                this.failedItems.splice(itemIndex, 1);
              }
            } else {
              console.error(
                `❌ Retry failed for ${failedItem.unit_id}_${failedItem.job_number}:`,
                result.error?.message
              );

              // 更新重試次數
              failedItem.retryCount++;
              failedItem.lastAttempt = Date.now();
              failedItem.error = result.error?.message || "Unknown error";
            }
          } catch (processingError) {
            console.error(
              `❌ Error processing retry result for ${failedItem.unit_id}_${failedItem.job_number}:`,
              processingError
            );

            // 更新重試次數
            failedItem.retryCount++;
            failedItem.lastAttempt = Date.now();
            failedItem.error =
              processingError instanceof Error
                ? processingError.message
                : String(processingError);
          }
        }

        // 顯示批次統計
        const successCount = results.filter((r) => r.success).length;
        const failureCount = results.length - successCount;
        console.log(
          `✅ Retry batch ${
            batchIndex + 1
          } completed: ${successCount} success, ${failureCount} failed`
        );

        // 顯示批次處理統計
        console.log(
          `📊 Batch processing: batchSize=${this.config.processing.batchSize}`
        );
      } catch (batchError) {
        console.error(`❌ Retry batch ${batchIndex + 1} failed:`, batchError);

        // 更新整個批次的重試次數
        for (const failedItem of batch) {
          failedItem.retryCount++;
          failedItem.lastAttempt = Date.now();
          failedItem.error =
            batchError instanceof Error
              ? batchError.message
              : String(batchError);
        }
      }
    }

    await this.storage.saveFailedItems(this.failedItems);
    console.log(
      `✅ Retry completed. ${this.failedItems.length} items still failed`
    );
  }

  private createDummyListRecord(unitId: string, jobNumber: string): ListRecord {
    return {
      date: 0,
      filename: "",
      brief: {
        type: "",
        title: "",
        companies: {
          ids: [],
          names: [],
          id_key: {},
          name_key: {},
        },
      },
      job_number: jobNumber,
      unit_id: unitId,
      unit_name: "",
      unit_api_url: "",
      tender_api_url: "",
      unit_url: "",
      url: "",
    };
  }

  private async saveProgress(): Promise<void> {
    try {
      await this.storage.saveProgress(this.progress);
    } catch (error) {
      console.error("Failed to save progress:", error);
    }
  }

  private async generateFinalReport(): Promise<void> {
    const duration = Date.now() - this.progress.startTime;
    const stats = await this.storage.getStorageStats();

    console.log("\n📊 Final Report");
    console.log("=".repeat(50));
    console.log(
      `📅 Date Range: ${this.progress.startDate} to ${this.progress.endDate}`
    );
    console.log(`⏱️ Duration: ${Math.round(duration / 1000)}s`);
    console.log(`✅ Completed: ${this.progress.completedItems}`);
    console.log(`❌ Failed: ${this.progress.failedItems}`);
    console.log(`📁 Total Files: ${stats.totalFiles}`);
    console.log(
      `💾 Total Size: ${Math.round(stats.totalSize / 1024 / 1024)}MB`
    );

    if (this.progress.failedItems > 0) {
      console.log(
        `\n⚠️ ${this.progress.failedItems} items failed. Run with --retry to retry failed items.`
      );
    }
  }

  async showStatus(): Promise<void> {
    const progress = await this.storage.loadProgress();
    const failedItems = await this.storage.loadFailedItems();
    const stats = await this.storage.getStorageStats();

    if (!progress) {
      console.log("No import in progress");
      return;
    }

    const duration = Date.now() - progress.startTime;
    const completionRate =
      (progress.completedItems / progress.totalItems) * 100;

    console.log("\n📊 Import Status");
    console.log("=".repeat(50));
    console.log(`📅 Date Range: ${progress.startDate} to ${progress.endDate}`);
    console.log(
      `📈 Progress: ${progress.completedItems}/${
        progress.totalItems
      } (${completionRate.toFixed(1)}%)`
    );
    console.log(`⏱️ Duration: ${Math.round(duration / 1000)}s`);
    console.log(`❌ Failed: ${failedItems.length}`);
    console.log(`📁 Files: ${stats.totalFiles}`);
    console.log(`💾 Size: ${Math.round(stats.totalSize / 1024 / 1024)}MB`);
  }

  private parseDate(dateStr: string): string {
    // Convert YYYYMMDD to YYYY-MM-DD
    if (dateStr.length === 8) {
      return `${dateStr.substr(0, 4)}-${dateStr.substr(4, 2)}-${dateStr.substr(
        6,
        2
      )}`;
    }
    return dateStr;
  }
}

// CLI Setup
const program = new Command();

program
  .name("import_daemon")
  .description("Import tender data from PCC API")
  .version("1.0.0");

program
  .command("import")
  .description("Import tender data for a date range")
  .requiredOption("--from <date>", "Start date (YYYYMMDD)")
  .requiredOption("--to <date>", "End date (YYYYMMDD)")
  .option("--config <path>", "Config file path", "./config/config.json")
  .option(
    "--limit <number>",
    "Limit number of tenders to process (for testing)",
    "0"
  )
  .action(async (options) => {
    try {
      const daemon = new ImportDaemon(options.config);
      const limit = parseInt(options.limit) || 0;
      await daemon.import(options.from, options.to, false, limit);
    } catch (error) {
      console.error("Import failed:", error);
      process.exit(1);
    }
  });

program
  .command("retry")
  .description("Retry failed items")
  .option("--config <path>", "Config file path", "./config/config.json")
  .action(async (options) => {
    try {
      const daemon = new ImportDaemon(options.config);
      await daemon.retryFailedItems();
    } catch (error) {
      console.error("Retry failed:", error);
      process.exit(1);
    }
  });

program
  .command("status")
  .description("Show import status")
  .option("--config <path>", "Config file path", "./config/config.json")
  .action(async (options) => {
    try {
      const daemon = new ImportDaemon(options.config);
      await daemon.showStatus();
    } catch (error) {
      console.error("Status check failed:", error);
      process.exit(1);
    }
  });

// Parse CLI arguments
program.parse();
