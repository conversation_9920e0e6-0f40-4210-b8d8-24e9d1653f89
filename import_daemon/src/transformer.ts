import * as fs from "fs";
import * as path from "path";
import {
  TenderDetailResponse,
  TenderRecord,
  ListRecord,
  TenderData,
  HistoryRecord,
} from "./types";

interface UnmappedField {
  sourceKey: string;
  targetField: string;
  sampleValue: any;
  recordCount: number;
  lastSeen: string;
}

interface UnmappedFieldsLog {
  [key: string]: UnmappedField;
}

export class FieldMapper {
  private unmappedFieldsFile = path.join(process.cwd(), "unmapped-fields.json");
  private unmappedFields: UnmappedFieldsLog = {};
  private pendingSave = false;
  private saveTimeout: NodeJS.Timeout | null = null;
  private isTestEnvironment =
    process.env.NODE_ENV === "test" || process.env.JEST_WORKER_ID !== undefined;

  constructor() {
    this.loadUnmappedFields();
  }

  private loadUnmappedFields() {
    try {
      if (fs.existsSync(this.unmappedFieldsFile)) {
        const data = fs.readFileSync(this.unmappedFieldsFile, "utf8");
        this.unmappedFields = JSON.parse(data);
      }
    } catch (error) {
      console.error("Failed to load unmapped fields log:", error);
    }
  }

  private saveUnmappedFields() {
    // 在測試環境中跳過文件寫入
    if (this.isTestEnvironment) {
      return;
    }

    try {
      fs.writeFileSync(
        this.unmappedFieldsFile,
        JSON.stringify(this.unmappedFields, null, 2)
      );
    } catch (error) {
      console.error("Failed to save unmapped fields log:", error);
    }
  }

  private debouncedSave() {
    // 在測試環境中跳過文件寫入
    if (this.isTestEnvironment) {
      return;
    }

    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.saveUnmappedFields();
      this.pendingSave = false;
    }, 1000); // 1秒後保存
  }

  private recordUnmappedField(
    sourceKey: string,
    targetField: string,
    sampleValue: any
  ) {
    const key = `${sourceKey}->${targetField}`;
    if (this.unmappedFields[key]) {
      this.unmappedFields[key].recordCount++;
      this.unmappedFields[key].lastSeen = new Date().toISOString();
    } else {
      this.unmappedFields[key] = {
        sourceKey,
        targetField,
        sampleValue,
        recordCount: 1,
        lastSeen: new Date().toISOString(),
      };
    }

    // 使用防抖保存，避免頻繁的文件寫入
    if (!this.pendingSave) {
      this.pendingSave = true;
      this.debouncedSave();
    }
  }

  // 添加手動保存方法，用於測試結束後保存
  public flushUnmappedFields() {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }
    this.saveUnmappedFields();
  }

  /**
   * 計算字符串相似度（簡單版本）
   */
  private calculateSimilarity(str1: string, str2: string): number {
    // 移除特殊字符，轉為小寫
    const clean1 = str1.replace(/[^\u4e00-\u9fff\w]/g, "").toLowerCase();
    const clean2 = str2.replace(/[^\u4e00-\u9fff\w]/g, "").toLowerCase();

    // 完全匹配
    if (clean1 === clean2) return 1.0;

    // 包含匹配
    if (clean1.includes(clean2) || clean2.includes(clean1)) {
      return 0.8;
    }

    // 簡單的字符重疊計算
    let matches = 0;
    const minLen = Math.min(clean1.length, clean2.length);
    for (let i = 0; i < minLen; i++) {
      if (clean1[i] === clean2[i]) matches++;
    }

    return matches / Math.max(clean1.length, clean2.length);
  }

  /**
   * 找到最佳匹配的目標字段
   */
  private findBestMatch(
    sourceKey: string,
    targetFields: string[]
  ): { field: string; score: number } {
    let bestMatch = { field: "", score: 0 };

    for (const targetField of targetFields) {
      const score = this.calculateSimilarity(sourceKey, targetField);
      if (score > bestMatch.score) {
        bestMatch = { field: targetField, score };
      }
    }

    return bestMatch;
  }

  /**
   * 轉換值的類型
   */
  private convertValue(
    value: any,
    targetType: "string" | "number" | "boolean" | "Date"
  ): any {
    if (value === null || value === undefined) return undefined;

    switch (targetType) {
      case "string":
        return String(value);

      case "number":
        if (typeof value === "string") {
          // 移除非數字字符，保留小數點
          const numStr = value.replace(/[^\d.-]/g, "");
          const num = parseFloat(numStr);
          return isNaN(num) ? 0 : num;
        }
        return Number(value) || 0;

      case "boolean":
        if (typeof value === "string") {
          return value === "是" || value === "true" || value === "1";
        }
        return Boolean(value);

      case "Date":
        if (typeof value === "string") {
          // 處理民國年格式 (113/06/25)
          const rocMatch = value.match(/(\d{2,3})\/(\d{1,2})\/(\d{1,2})/);
          if (rocMatch) {
            const year =
              parseInt(rocMatch[1]) + (rocMatch[1].length === 3 ? 1911 : 2000);
            const month = parseInt(rocMatch[2]);
            const day = parseInt(rocMatch[3]);
            return new Date(year, month - 1, day);
          }

          // 處理其他日期格式
          const date = new Date(value);
          return isNaN(date.getTime()) ? new Date() : date;
        }
        return new Date(value);

      default:
        return value;
    }
  }

  /**
   * 檢查是否應該覆蓋現有值
   */
  private shouldOverwriteValue(
    existingValue: any,
    newValue: any,
    targetType: string
  ): boolean {
    // 如果沒有現有值，總是覆蓋
    if (existingValue === null || existingValue === undefined) {
      return true;
    }

    // 如果新值是 null 或 undefined，不覆蓋
    if (newValue === null || newValue === undefined) {
      return false;
    }

    // 對於數字類型，如果新值是 0 且現有值不是 0，不覆蓋
    if (targetType === "number") {
      if (newValue === 0 && existingValue !== 0) {
        return false;
      }
    }

    // 對於字符串類型，如果新值是空字符串且現有值不是空，不覆蓋
    if (targetType === "string") {
      if (newValue === "" && existingValue !== "") {
        return false;
      }
    }

    // 其他情況，覆蓋
    return true;
  }

  /**
   * 映射字段到目標對象
   */
  mapFields(
    sourceData: any,
    targetSchema: any,
    targetName: string = "TenderData"
  ): any {
    const result: any = {};
    const targetFields = Object.keys(targetSchema);

    // 遍歷源數據的所有字段
    for (const [sourceKey, sourceValue] of Object.entries(sourceData)) {
      if (sourceValue === null || sourceValue === undefined) continue;

      let mapped = false;

      // 處理帶有冒號的結構化字段 (例如: "機關資料:機關代碼")
      if (sourceKey.includes(":")) {
        const [prefix, fieldName] = sourceKey.split(":", 2);

        // 只在對應的嵌套對象中處理這個字段
        if (targetName === prefix) {
          // 直接映射到當前對象中
          let targetField = targetFields.find((field) => field === fieldName);

          // 如果沒有精確匹配，嘗試模糊匹配
          if (!targetField) {
            const bestMatch = this.findBestMatch(fieldName, targetFields);
            if (bestMatch.score > 0.6) {
              targetField = bestMatch.field;
            }
          }

          if (targetField) {
            const targetType = this.getFieldType(targetSchema[targetField]);
            const convertedValue = this.convertValue(sourceValue, targetType);

            // 檢查是否應該覆蓋現有值
            if (
              this.shouldOverwriteValue(
                result[targetField],
                convertedValue,
                targetType
              )
            ) {
              result[targetField] = convertedValue;
            }
            mapped = true;
          } else {
            // 如果找不到匹配，記錄未映射字段並硬塞
            this.recordUnmappedField(sourceKey, targetName, sourceValue);
            if (
              this.shouldOverwriteValue(
                result[fieldName],
                sourceValue,
                "string"
              )
            ) {
              result[fieldName] = sourceValue;
            }
            mapped = true;
          }
        } else if (targetName === "TenderData") {
          // 在頂層對象中，檢查是否有對應的嵌套對象字段
          const nestedField = targetFields.find((field) => field === prefix);

          if (
            nestedField &&
            targetSchema[nestedField] &&
            typeof targetSchema[nestedField] === "object"
          ) {
            // 確保嵌套對象存在
            if (!result[nestedField]) {
              result[nestedField] = {};
            }

            // 獲取嵌套對象的schema
            const nestedSchema = targetSchema[nestedField];
            const nestedFields = Object.keys(nestedSchema);

            // 在嵌套對象中尋找匹配的字段
            let nestedTargetField = nestedFields.find(
              (field) => field === fieldName
            );

            // 如果沒有精確匹配，嘗試模糊匹配
            if (!nestedTargetField) {
              const bestMatch = this.findBestMatch(fieldName, nestedFields);
              if (bestMatch.score > 0.6) {
                nestedTargetField = bestMatch.field;
              }
            }

            if (nestedTargetField) {
              const targetType = this.getFieldType(
                nestedSchema[nestedTargetField]
              );
              const convertedValue = this.convertValue(sourceValue, targetType);

              // 檢查是否應該覆蓋嵌套對象中的現有值
              if (
                this.shouldOverwriteValue(
                  result[nestedField][nestedTargetField],
                  convertedValue,
                  targetType
                )
              ) {
                result[nestedField][nestedTargetField] = convertedValue;
              }
              mapped = true;

              // 同時檢查是否也需要映射到頂級字段
              // 例如：機關資料:機關名稱 -> 機關資料.機關名稱 + 機關名稱
              const topLevelField = targetFields.find(
                (field) => field === fieldName
              );
              if (topLevelField) {
                const topLevelType = this.getFieldType(
                  targetSchema[topLevelField]
                );
                const topLevelConvertedValue = this.convertValue(
                  sourceValue,
                  topLevelType
                );

                // 檢查是否應該覆蓋頂級字段的現有值
                if (
                  this.shouldOverwriteValue(
                    result[topLevelField],
                    topLevelConvertedValue,
                    topLevelType
                  )
                ) {
                  result[topLevelField] = topLevelConvertedValue;
                }
              }
            } else {
              // 如果在嵌套對象中找不到匹配，記錄未映射字段
              this.recordUnmappedField(
                sourceKey,
                `${targetName}.${prefix}`,
                sourceValue
              );
              // 仍然硬塞到嵌套對象中
              if (
                this.shouldOverwriteValue(
                  result[nestedField][fieldName],
                  sourceValue,
                  "string"
                )
              ) {
                result[nestedField][fieldName] = sourceValue;
              }
              mapped = true;
            }
          }
        }
        // 如果不是對應的嵌套對象，也不是頂層對象，則跳過這個字段
        if (!mapped) {
          continue;
        }
      }

      // 如果還沒有映射，嘗試直接映射到當前層級的字段
      if (!mapped) {
        // 1. 精確匹配
        let targetField = targetFields.find((field) => field === sourceKey);

        // 2. 關鍵詞匹配（移除前綴後匹配）
        if (!targetField) {
          const cleanSourceKey = sourceKey.includes(":")
            ? sourceKey.split(":").pop()!
            : sourceKey;
          targetField = targetFields.find((field) => field === cleanSourceKey);
        }

        // 3. 包含匹配
        if (!targetField) {
          const cleanSourceKey = sourceKey.includes(":")
            ? sourceKey.split(":").pop()!
            : sourceKey;
          targetField = targetFields.find(
            (field) =>
              field.includes(cleanSourceKey) || cleanSourceKey.includes(field)
          );
        }

        // 4. 模糊匹配
        if (!targetField) {
          const bestMatch = this.findBestMatch(sourceKey, targetFields);
          if (bestMatch.score > 0.6) {
            // 相似度閾值
            targetField = bestMatch.field;
          }
        }

        // 5. 如果找到匹配，進行類型轉換並賦值
        if (targetField) {
          const targetType = this.getFieldType(targetSchema[targetField]);
          const convertedValue = this.convertValue(sourceValue, targetType);

          // 檢查是否應該覆蓋現有值
          if (
            this.shouldOverwriteValue(
              result[targetField],
              convertedValue,
              targetType
            )
          ) {
            result[targetField] = convertedValue;
          }
        } else {
          // 6. 未找到匹配，記錄到異常檔案並硬塞
          this.recordUnmappedField(sourceKey, targetName, sourceValue);
          if (
            this.shouldOverwriteValue(result[sourceKey], sourceValue, "string")
          ) {
            result[sourceKey] = sourceValue; // 硬塞進去
          }
        }
      }
    }

    return result;
  }

  /**
   * 獲取字段的預期類型
   */
  private getFieldType(
    schemaValue: any
  ): "string" | "number" | "boolean" | "Date" {
    if (schemaValue === String || typeof schemaValue === "string")
      return "string";
    if (schemaValue === Number || typeof schemaValue === "number")
      return "number";
    if (schemaValue === Boolean || typeof schemaValue === "boolean")
      return "boolean";
    if (schemaValue === Date || schemaValue instanceof Date) return "Date";
    return "string"; // 默認為字符串
  }
}

export class Transformer {
  private fieldMapper: FieldMapper;

  constructor() {
    this.fieldMapper = new FieldMapper();
  }

  /**
   * 將 API 回應轉換成 TenderRecord 格式
   */
  transformTenderDetail(
    response: TenderDetailResponse,
    listRecord: ListRecord
  ): TenderRecord[] {
    if (!response.records || response.records.length === 0) {
      throw new Error("No tender records found in API response");
    }

    return response.records.map((record) => {
      if (!record.detail) {
        throw new Error(
          `Missing detail for tender ${record.unit_id}_${record.job_number}`
        );
      }

      return {
        ...record,
        detail: {
          ...record.detail,
          fetched_at: record.detail.fetched_at || new Date().toISOString(),
        },
      };
    });
  }

  /**
   * 從 ListRecord 創建基本的 TenderRecord 結構
   */
  createBasicTenderRecord(listRecord: ListRecord, error: string): TenderRecord {
    return {
      date: listRecord.date,
      filename: listRecord.filename,
      brief: listRecord.brief,
      job_number: listRecord.job_number,
      unit_id: listRecord.unit_id,
      detail: {
        type: "error",
        url: "",
        機關資料: {
          機關代碼: listRecord.unit_id,
          機關名稱: listRecord.unit_name,
          單位名稱: "",
          機關地址: "",
          聯絡人: "",
          聯絡電話: "",
          傳真號碼: "",
        },
        已公告資料: {
          標案案號: listRecord.job_number,
          招標方式: "",
          決標方式: "",
          是否依政府採購法施行細則第64條之2辦理: "",
          新增公告傳輸次數: "",
          是否依據採購法第106條第1項第1款辦理: "",
          標案名稱: listRecord.brief.title,
          決標資料類別: "",
          是否屬共同供應契約採購: "",
          是否屬二以上機關之聯合採購: "",
          是否複數決標: "",
          是否共同投標: "",
          標的分類: listRecord.brief.category || "",
          是否屬統包: "",
          是否應依公共工程專業技師簽證規則實施技師簽證: "",
          開標時間: "",
          原公告日期: "",
          採購金額級距: "",
          辦理方式: "",
          是否適用條約或協定之採購: {
            是否適用WTO政府採購協定: "",
            是否適用臺紐經濟合作協定: "",
            是否適用臺星經濟夥伴協定: "",
          },
          預算金額是否公開: "",
          預算金額: "",
          是否受機關補助: "",
          履約地點: "",
          履約地點含地區: "",
          是否含特別預算: "",
          歸屬計畫類別: "",
          本案採購契約是否採用主管機關訂定之範本: "",
          是否屬災區重建工程: "",
        },
        投標廠商: {
          投標廠商家數: "0",
        },
        決標品項: {
          決標品項數: "0",
        },
        決標資料: {
          決標公告序號: "",
          決標日期: "",
          決標公告日期: "",
          是否刊登公報: "",
          底價金額: "",
          底價金額是否公開: "",
          總決標金額: "",
          總決標金額是否公開: "",
          契約是否訂有依物價指數調整價金規定: "",
          履約執行機關: "",
          附加說明: `Error fetching details: ${error}`,
        },
        fetched_at: new Date().toISOString(),
      },
      unit_name: listRecord.unit_name,
      unit_api_url: listRecord.unit_api_url,
      tender_api_url: listRecord.tender_api_url,
      unit_url: listRecord.unit_url,
      url: listRecord.url,
    };
  }

  /**
   * 將 TenderRecord 轉換成 TenderData 格式 (標準化格式)
   */
  transformToTenderData(record: TenderRecord): TenderData {
    const detail = record.detail;
    if (!detail) {
      throw new Error(
        `Missing detail for record: ${record.unit_id}_${record.job_number}`
      );
    }

    const now = new Date();

    // Extract category information
    const fullCategory =
      (detail as any)?.["採購資料:標的分類"] ||
      (detail as any)?.["已公告資料:標的分類"] ||
      "";

    const categoryMatch = fullCategory.match(/<(.+?)>(\d+)(.+)/);
    const categoryType = this.determineCategoryType(fullCategory);
    const categoryCode = categoryMatch ? categoryMatch[2] : "";

    // Extract budget amount
    const budgetStr =
      (detail as any)?.["採購資料:預算金額"] ||
      (detail as any)?.["已公告資料:預算金額"] ||
      "";
    const budgetAmount = this.parseBudgetAmount(budgetStr);

    // Extract dates
    const announcementDate =
      this.parseDate((detail as any)?.["招標資料:公告日"]) ||
      new Date(record.date);
    const submissionDeadline = this.parseDate(
      (detail as any)?.["領投開標:截止投標"]
    );
    const openingTime = this.parseDate((detail as any)?.["領投開標:開標時間"]);

    return {
      // === Primary Identifiers ===
      tender_id: record.job_number,
      case_number:
        (detail as any)?.["採購資料:標案案號"] ||
        (detail as any)?.["已公告資料:標案案號"] ||
        record.job_number,
      project_id: record.job_number,
      pcc_main_key: (detail as any)?.pkPmsMain || "",

      // === Basic Information ===
      title:
        (detail as any)?.["採購資料:標案名稱"] ||
        record.brief?.title ||
        "Unknown",
      description: this.generateDescription(record),
      category: fullCategory,
      category_code: categoryCode,
      category_type: categoryType,

      // === Agency Information ===
      agency_code: record.unit_id || "",
      agency_name:
        (detail as any)?.["機關資料:機關名稱"] || record.unit_name || "",
      agency_unit: (detail as any)?.["機關資料:單位名稱"] || "",
      agency_address: (detail as any)?.["機關資料:機關地址"] || "",
      contact_person: (detail as any)?.["機關資料:聯絡人"] || "",
      contact_phone: (detail as any)?.["機關資料:聯絡電話"] || "",
      contact_fax: (detail as any)?.["機關資料:傳真號碼"] || "",
      contact_email: (detail as any)?.["機關資料:電子郵件信箱"] || "",

      // === Financial Information ===
      budget_amount: budgetAmount,
      budget_disclosed: this.parseBoolean(
        (detail as any)?.["採購資料:預算金額是否公開"]
      ),
      budget_range: (detail as any)?.["採購資料:採購金額級距"] || "",
      award_amount: undefined,
      award_disclosed: undefined,
      reserve_price: undefined,
      reserve_disclosed: undefined,

      // === Dates ===
      announcement_date: announcementDate,
      original_announcement_date: undefined,
      submission_deadline: submissionDeadline,
      opening_time: openingTime,
      award_date: undefined,

      // === Status and Type ===
      status: this.determineStatus(detail.type),
      tender_type: this.determineTenderType(detail.type),
      procurement_method: (detail as any)?.["招標資料:招標方式"] || "",
      award_method: (detail as any)?.["招標資料:決標方式"] || "",

      // === Location and Performance ===
      performance_location: (detail as any)?.["其他:履約地點"] || "",
      performance_location_detail: "",
      performance_period: (detail as any)?.["其他:履約期限"] || "",

      // === Compliance and Regulations ===
      is_wto_gpa: this.parseBoolean(
        (detail as any)?.[
          "採購資料:是否適用條約或協定之採購:是否適用WTO政府採購協定(GPA)"
        ]
      ),
      is_anztec: this.parseBoolean(
        (detail as any)?.[
          "採購資料:是否適用條約或協定之採購:是否適用臺紐經濟合作協定(ANZTEC)"
        ]
      ),
      is_astep: this.parseBoolean(
        (detail as any)?.[
          "採購資料:是否適用條約或協定之採購:是否適用臺星經濟夥伴協定(ASTEP)"
        ]
      ),
      is_multiple_award: this.parseBoolean(
        (detail as any)?.["招標資料:是否複數決標"]
      ),
      is_joint_procurement: this.parseBoolean(
        (detail as any)?.[
          "招標資料:是否屬二以上機關之聯合採購(不適用共同供應契約規定)"
        ]
      ),
      is_electronic_bidding: this.parseBoolean(
        (detail as any)?.["領投開標:是否提供電子投標"]
      ),
      requires_deposit: this.parseBoolean(
        (detail as any)?.["領投開標:是否須繳納押標金"]
      ),
      deposit_amount: this.parseNumber(
        (detail as any)?.["領投開標:是否須繳納押標金:押標金額度"]
      ),

      // === Technical Requirements ===
      is_turnkey: this.parseBoolean((detail as any)?.["招標資料:是否屬統包"]),
      requires_engineer_cert: this.parseBoolean(
        (detail as any)?.[
          "招標資料:是否應依公共工程專業技師簽證規則實施技師簽證"
        ]
      ),
      is_special_procurement: this.parseBoolean(
        (detail as any)?.["招標資料:是否屬特殊採購"]
      ),

      // === Metadata ===
      ai_score: this.calculateAiScore(record),
      source_url: detail.url || "",
      publish_date: new Date(record.date),
      created_at: now,
      updated_at: now,
      data_version: "1.0",

      // === Bidding Information ===
      bidder_count: undefined,
      document_fee: this.parseNumber(
        (detail as any)?.["領投開標:是否提供電子領標:機關文件費(機關實收)"]
      ),

      // === Additional Flags ===
      is_government_subsidy: this.parseBoolean(
        (detail as any)?.["採購資料:是否受機關補助"]
      ),
      is_disaster_reconstruction: false, // TODO: 需要從其他字段推斷
      is_sensitive_security: this.parseBoolean(
        (detail as any)?.[
          "採購資料:本採購是否屬「具敏感性或國安(含資安)疑慮之業務範疇」採購"
        ]
      ),
      is_published_gazette: this.parseBoolean(
        (detail as any)?.["其他:是否刊登公報"]
      ),

      // === History and Tracking ===
      history: [
        {
          date: record.date.toString(),
          status: this.determineStatus(detail.type),
          tenderType: this.determineTenderType(detail.type),
          metadata: {
            filename: record.filename || "",
            recordDate: parseInt(record.date.toString().replace(/-/g, "")),
            fetchedAt: detail.fetched_at || new Date().toISOString(),
          },
        },
      ],

      // === Related Data ===
      bidders: [],
      award_items: [],
    };
  }

  // 輔助方法
  private calculateAiScore(record: TenderRecord): number {
    let score = 5;

    const budgetStr =
      (record.detail as any)?.["採購資料:預算金額"] ||
      (record.detail as any)?.["已公告資料:預算金額"] ||
      record.detail?.已公告資料?.預算金額 ||
      "";
    const budget = parseInt(budgetStr.replace(/[^\d]/g, "")) || 0;

    if (budget > 1000000) score += 2;
    else if (budget > 100000) score += 1;

    const title = record.brief?.title || "";
    if (
      title.includes("資訊") ||
      title.includes("系統") ||
      title.includes("軟體")
    ) {
      score += 1;
    }

    return Math.min(10, Math.max(1, score));
  }

  private determineStatus(
    type: string
  ): "bidding" | "awarded" | "failed" | "open" | "closed" {
    if (type.includes("決標")) return "awarded";
    if (type.includes("無法決標")) return "failed";
    if (type.includes("公開招標")) return "open";
    return "bidding";
  }

  private generateDescription(record: TenderRecord): string {
    const parts = [];
    const title = record.brief?.title || "";
    if (title) parts.push(title);

    const method = (record.detail as any)?.["招標資料:招標方式"] || "";
    if (method) parts.push(`招標方式: ${method}`);

    const budget =
      (record.detail as any)?.["採購資料:預算金額"] ||
      (record.detail as any)?.["已公告資料:預算金額"] ||
      "";
    if (budget) parts.push(`預算: ${budget}`);

    return parts.join(" | ");
  }

  /**
   * 將多筆 TenderRecord 合併成一個 TenderData
   */
  mergeRecordsToTenderData(records: TenderRecord[]): TenderData {
    if (!records || records.length === 0) {
      throw new Error("No records to merge");
    }

    const sortedRecords = records.sort((a, b) => b.date - a.date);
    const latestRecord = sortedRecords[0];

    let tenderData = this.transformToTenderData(latestRecord);

    // 確保必要字段存在
    if (!tenderData.project_id || tenderData.project_id === "undefined") {
      tenderData.project_id = `${latestRecord.unit_id}_${latestRecord.job_number}`;
      console.warn("Fixed missing project_id:", tenderData.project_id);
    }

    if (!tenderData.publish_date) {
      tenderData.publish_date = new Date(latestRecord.date);
      console.warn("Fixed missing publish_date:", tenderData.publish_date);
    }

    // 建立歷史記錄
    const history: HistoryRecord[] = sortedRecords
      .reverse()
      .map((record) => this.createHistoryRecord(record));

    tenderData.history = history;

    return tenderData;
  }

  private createHistoryRecord(record: TenderRecord): HistoryRecord {
    const detail = record.detail;
    const tenderType = this.determineTenderType(detail?.type || "");
    const status = this.determineStatus(detail?.type || "");

    return {
      date: this.formatRecordDate(record.date),
      status,
      tenderType,
      metadata: {
        filename: record.filename,
        recordDate: record.date,
        fetchedAt: detail?.fetched_at,
      },
    };
  }

  private formatRecordDate(date: number): string {
    const dateStr = date.toString();
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${year}-${month}-${day}`;
  }

  /**
   * 輔助方法：確定採購類型
   */
  private determineCategoryType(
    category: string
  ): "goods" | "services" | "construction" | "other" {
    if (category.includes("工程類")) return "construction";
    if (category.includes("勞務類")) return "services";
    if (category.includes("財物類")) return "goods";
    return "other";
  }

  /**
   * 輔助方法：確定標案類型
   */
  private determineTenderType(
    type: string
  ): "tender" | "award" | "failure" | "amendment" | "cancellation" {
    if (type.includes("決標")) return "award";
    if (type.includes("流標") || type.includes("廢標")) return "failure";
    if (type.includes("更正") || type.includes("變更")) return "amendment";
    if (type.includes("取消")) return "cancellation";
    return "tender";
  }

  /**
   * 輔助方法：解析布林值
   */
  private parseBoolean(value: string | undefined): boolean {
    if (!value) return false;
    return value === "是" || value === "Y" || value === "true" || value === "1";
  }

  /**
   * 輔助方法：解析數字
   */
  private parseNumber(value: string | undefined): number | undefined {
    if (!value || typeof value !== "string") return undefined;
    const num = parseInt(value.replace(/[^\d]/g, ""));
    return isNaN(num) ? undefined : num;
  }

  /**
   * 解析預算金額字串，將 "193,200元" 轉換為數字
   */
  private parseBudgetAmount(budgetStr: string | undefined): number {
    if (!budgetStr || typeof budgetStr !== "string") return 0;

    // 移除所有非數字字符（除了小數點）
    const cleanStr = budgetStr.replace(/[^\d.]/g, "");
    const amount = parseFloat(cleanStr);

    return isNaN(amount) ? 0 : amount;
  }

  /**
   * 解析日期字串，支援多種格式
   */
  private parseDate(dateStr: string | undefined): Date {
    if (!dateStr || typeof dateStr !== "string") return new Date();

    try {
      // 如果是 ISO 格式或標準日期格式
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return date;
      }

      // 嘗試解析民國年格式 (113/12/31)
      const rocMatch = dateStr.match(/(\d{2,3})\/(\d{1,2})\/(\d{1,2})/);
      if (rocMatch) {
        const year =
          parseInt(rocMatch[1]) + (rocMatch[1].length === 3 ? 1911 : 2000);
        const month = parseInt(rocMatch[2]) - 1; // JavaScript months are 0-based
        const day = parseInt(rocMatch[3]);
        return new Date(year, month, day);
      }

      // 嘗試解析其他格式 (2024-12-31, 2024/12/31)
      const standardMatch = dateStr.match(
        /(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})/
      );
      if (standardMatch) {
        const year = parseInt(standardMatch[1]);
        const month = parseInt(standardMatch[2]) - 1;
        const day = parseInt(standardMatch[3]);
        return new Date(year, month, day);
      }

      return new Date();
    } catch (error) {
      return new Date();
    }
  }

  // 保持原有的其他方法
  validateTenderRecord(record: TenderRecord): boolean {
    try {
      if (!record.unit_id || !record.job_number) return false;
      if (!record.detail) return false;
      return true;
    } catch (error) {
      console.error("Validation error:", error);
      return false;
    }
  }

  cleanTenderRecord(record: TenderRecord): TenderRecord {
    const cleanString = (str: string): string => str?.trim?.() || "";
    const cleaned = JSON.parse(JSON.stringify(record));

    if (cleaned.detail?.機關資料) {
      Object.keys(cleaned.detail.機關資料).forEach((key) => {
        if (typeof cleaned.detail.機關資料[key] === "string") {
          cleaned.detail.機關資料[key] = cleanString(
            cleaned.detail.機關資料[key]
          );
        }
      });
    }

    return cleaned;
  }

  isValidTenderType(record: TenderRecord): boolean {
    const validTypes = [
      "公開招標公告",
      "決標公告",
      "無法決標公告",
      "更正公告",
      "撤銷公告",
    ];
    return validTypes.includes(record.detail?.type || "");
  }

  extractKeyInfo(record: TenderRecord): {
    id: string;
    title: string;
    agency: string;
    amount: string;
    type: string;
    publishDate: string;
  } {
    return {
      id: `${record.unit_id}_${record.job_number}`,
      title: record.brief?.title || "Unknown",
      agency: record.unit_name || "Unknown",
      amount:
        (record.detail as any)?.["採購資料:預算金額"] ||
        (record.detail as any)?.["已公告資料:預算金額"] ||
        "Unknown",
      type: record.detail?.type || "Unknown",
      publishDate: record.date.toString(),
    };
  }
}
