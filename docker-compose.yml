version: "3.8"

# This Docker Compose file sets up the development environment for the BidAcumen application.
# It includes the frontend, backend, a PostgreSQL database, and a Redis cache.

services:
  # Database Service (PostgreSQL)
  db:
    image: postgres:15-alpine
    container_name: bidacumen_db
    environment:
      - POSTGRES_USER=bidradaradmin
      - POSTGRES_PASSWORD=sdflksjdflk49485435sDDWE
      - POSTGRES_DB=bidradar
    volumes:
      # Persists database data in a local directory for easy portability
      - ./data/postgres:/var/lib/postgresql/data
    ports:
      - "25432:5432"
    networks:
      - bidacumen_net
    restart: unless-stopped

  # Cache Service (Redis)
  redis:
    image: redis:7-alpine
    container_name: bidacumen_cache
    volumes:
      # Persists Redis data in a local directory
      - ./data/redis:/data
    networks:
      - bidacumen_net
    restart: unless-stopped

# Network for inter-service communication
networks:
  bidacumen_net:
    driver: bridge
